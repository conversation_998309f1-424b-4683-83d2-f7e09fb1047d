"""
Feature Audit and Progress Tracking System
Provides comprehensive analysis of feature completion and project status.
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
from pathlib import Path
import json
from enum import Enum

from utils.logger import get_logger

logger = get_logger(__name__)

class FeatureStatus(str, Enum):
    """Status of features."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    TESTING = "testing"
    BLOCKED = "blocked"
    DEPRECATED = "deprecated"

class Priority(str, Enum):
    """Priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class Feature(BaseModel):
    """Model for individual features."""
    id: str
    name: str
    description: str
    category: str
    status: FeatureStatus
    priority: Priority
    completion_percentage: float = Field(ge=0.0, le=100.0)
    
    # Dependencies
    depends_on: List[str] = Field(default_factory=list)
    blocks: List[str] = Field(default_factory=list)
    
    # Tracking
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    last_updated: datetime = Field(default_factory=datetime.now)
    
    # Details
    implementation_notes: str = ""
    test_status: str = ""
    known_issues: List[str] = Field(default_factory=list)
    
    # Effort estimation
    estimated_hours: Optional[float] = None
    actual_hours: Optional[float] = None

class ProjectMetrics(BaseModel):
    """Overall project metrics."""
    total_features: int
    completed_features: int
    in_progress_features: int
    not_started_features: int
    blocked_features: int
    
    overall_completion_percentage: float
    completion_by_category: Dict[str, float]
    completion_by_priority: Dict[str, float]
    
    estimated_total_hours: float
    actual_hours_spent: float
    remaining_estimated_hours: float
    
    last_updated: datetime = Field(default_factory=datetime.now)

class FeatureAudit:
    """Comprehensive feature audit and tracking system."""
    
    def __init__(self):
        self.data_dir = Path("data/feature_audit")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.features: Dict[str, Feature] = {}
        self._load_features()
        
        # Initialize features if empty
        if not self.features:
            self._initialize_default_features()
    
    def _initialize_default_features(self):
        """Initialize the default feature set for JobFlow."""
        
        default_features = [
            # Core Infrastructure
            Feature(
                id="core_setup",
                name="Core Project Setup",
                description="Basic project structure, dependencies, and configuration",
                category="Infrastructure",
                status=FeatureStatus.COMPLETED,
                priority=Priority.CRITICAL,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Completed with uv package manager, Pydantic v2, Streamlit"
            ),
            
            Feature(
                id="database_system",
                name="Database System",
                description="SQL database with SQLAlchemy, models, and data management",
                category="Infrastructure",
                status=FeatureStatus.COMPLETED,
                priority=Priority.CRITICAL,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="SQLite database with proper models and encryption support"
            ),
            
            Feature(
                id="logging_system",
                name="Logging System",
                description="Privacy-aware logging with sensitive data filtering",
                category="Infrastructure",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now()
            ),
            
            # Design System & Branding
            Feature(
                id="design_system",
                name="Design System & Branding",
                description="Custom color palette, Funnel Sans font, pet-friendly UI",
                category="Design",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Implemented with new color palette and Funnel Sans font"
            ),
            
            Feature(
                id="responsive_design",
                name="Responsive Design",
                description="Mobile-friendly interface with responsive components",
                category="Design",
                status=FeatureStatus.IN_PROGRESS,
                priority=Priority.MEDIUM,
                completion_percentage=70.0,
                implementation_notes="Basic responsive design implemented, needs mobile optimization"
            ),
            
            # AI Processing & Cost Management
            Feature(
                id="openrouter_integration",
                name="OpenRouter API Integration",
                description="Integration with OpenRouter for AI processing",
                category="AI",
                status=FeatureStatus.COMPLETED,
                priority=Priority.CRITICAL,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Full OpenRouter client with multiple model support"
            ),
            
            Feature(
                id="cv_processing",
                name="CV Processing System",
                description="Async CV upload, text extraction, and AI analysis",
                category="AI",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Supports PDF, DOCX, TXT with background processing"
            ),
            
            Feature(
                id="cost_tracking",
                name="Cost Tracking & Monitoring",
                description="Real-time cost monitoring and usage tracking",
                category="AI",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Comprehensive cost tracking with alerts and limits"
            ),
            
            Feature(
                id="job_matching",
                name="AI Job Matching",
                description="AI-powered job matching against CV analysis",
                category="AI",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Full job matching with match scores, skill analysis, and recommendations"
            ),

            Feature(
                id="cover_letter_generation",
                name="AI Cover Letter Generation",
                description="Personalized cover letter generation for each job",
                category="AI",
                status=FeatureStatus.COMPLETED,
                priority=Priority.MEDIUM,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Complete cover letter generation with customization options and library"
            ),
            
            # User Interface
            Feature(
                id="onboarding_flow",
                name="User Onboarding Flow",
                description="Step-by-step user setup and profile creation",
                category="UI",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now()
            ),
            
            Feature(
                id="dashboard",
                name="Main Dashboard",
                description="Analytics dashboard with metrics and insights",
                category="UI",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now()
            ),
            
            Feature(
                id="job_search_ui",
                name="Job Search Interface",
                description="Modern job search with filters and results display",
                category="UI",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now()
            ),
            
            Feature(
                id="ai_assistant_ui",
                name="AI Assistant Interface",
                description="UI for AI features, cost monitoring, and CV processing",
                category="UI",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Comprehensive AI interface with cost monitoring"
            ),
            
            # Automation & Queue System
            Feature(
                id="queue_system",
                name="Automated Queue System",
                description="Background job processing with state management",
                category="Automation",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Full queue system with auto-run capabilities"
            ),
            
            Feature(
                id="browser_automation",
                name="Browser Automation",
                description="Playwright-based job application automation",
                category="Automation",
                status=FeatureStatus.IN_PROGRESS,
                priority=Priority.HIGH,
                completion_percentage=75.0,
                implementation_notes="Enhanced framework with LinkedIn and Indeed automation classes"
            ),
            
            Feature(
                id="auto_run_mode",
                name="Auto-Run Mode",
                description="Automated job application with scheduling and limits",
                category="Automation",
                status=FeatureStatus.COMPLETED,
                priority=Priority.MEDIUM,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Integrated with queue system and cost tracking"
            ),
            
            # API & Integration
            Feature(
                id="rest_api",
                name="REST API Endpoints",
                description="External API for communication and integrations",
                category="API",
                status=FeatureStatus.COMPLETED,
                priority=Priority.MEDIUM,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="FastAPI with comprehensive endpoints"
            ),
            
            Feature(
                id="connection_testing",
                name="Connection Testing",
                description="Validation and testing for all integrations",
                category="API",
                status=FeatureStatus.COMPLETED,
                priority=Priority.MEDIUM,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="API endpoints for testing portals and AI service"
            ),
            
            Feature(
                id="portal_integrations",
                name="Job Portal Integrations",
                description="Integration with multiple job portals",
                category="Integration",
                status=FeatureStatus.IN_PROGRESS,
                priority=Priority.HIGH,
                completion_percentage=30.0,
                implementation_notes="We Work Remotely RSS implemented, others need work"
            ),
            
            # Testing & Quality
            Feature(
                id="unit_tests",
                name="Unit Testing",
                description="Comprehensive unit test coverage",
                category="Testing",
                status=FeatureStatus.IN_PROGRESS,
                priority=Priority.HIGH,
                completion_percentage=40.0,
                implementation_notes="AI features test suite implemented, need more coverage"
            ),
            
            Feature(
                id="integration_tests",
                name="Integration Testing",
                description="End-to-end integration testing",
                category="Testing",
                status=FeatureStatus.NOT_STARTED,
                priority=Priority.MEDIUM,
                completion_percentage=0.0,
                estimated_hours=8.0
            ),
            
            # Documentation & Deployment
            Feature(
                id="documentation",
                name="Documentation",
                description="User guides, API docs, and developer documentation",
                category="Documentation",
                status=FeatureStatus.IN_PROGRESS,
                priority=Priority.MEDIUM,
                completion_percentage=20.0,
                implementation_notes="Basic README and project context, needs expansion"
            ),
            
            Feature(
                id="deployment_system",
                name="Deployment System",
                description="Docker, CI/CD, and deployment automation",
                category="Deployment",
                status=FeatureStatus.NOT_STARTED,
                priority=Priority.LOW,
                completion_percentage=0.0,
                estimated_hours=6.0
            ),
            
            # Security & Privacy
            Feature(
                id="security_features",
                name="Security Features",
                description="Authentication, encryption, and security measures",
                category="Security",
                status=FeatureStatus.COMPLETED,
                priority=Priority.HIGH,
                completion_percentage=100.0,
                completed_at=datetime.now(),
                implementation_notes="Complete authentication system with session management and security features"
            ),
            
            Feature(
                id="privacy_controls",
                name="Privacy Controls",
                description="GDPR compliance, data export, and privacy settings",
                category="Security",
                status=FeatureStatus.IN_PROGRESS,
                priority=Priority.HIGH,
                completion_percentage=50.0,
                implementation_notes="Local storage implemented, needs GDPR compliance features"
            )
        ]
        
        # Add features to the system
        for feature in default_features:
            self.features[feature.id] = feature
        
        # Save to disk
        self._save_features()
        logger.info(f"Initialized {len(default_features)} default features")
    
    def _load_features(self):
        """Load features from disk."""
        features_file = self.data_dir / "features.json"
        if features_file.exists():
            try:
                with open(features_file, 'r') as f:
                    features_data = json.load(f)
                
                for feature_id, feature_data in features_data.items():
                    self.features[feature_id] = Feature(**feature_data)
                
                logger.info(f"Loaded {len(self.features)} features from disk")
                
            except Exception as e:
                logger.error(f"Error loading features: {str(e)}")
    
    def _save_features(self):
        """Save features to disk."""
        features_file = self.data_dir / "features.json"
        try:
            features_data = {
                feature_id: feature.model_dump(mode='json')
                for feature_id, feature in self.features.items()
            }
            
            with open(features_file, 'w') as f:
                json.dump(features_data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error saving features: {str(e)}")
    
    def get_project_metrics(self) -> ProjectMetrics:
        """Calculate comprehensive project metrics."""
        total_features = len(self.features)
        
        # Count by status
        status_counts = {}
        for feature in self.features.values():
            status_counts[feature.status] = status_counts.get(feature.status, 0) + 1
        
        completed_features = status_counts.get(FeatureStatus.COMPLETED, 0)
        in_progress_features = status_counts.get(FeatureStatus.IN_PROGRESS, 0)
        not_started_features = status_counts.get(FeatureStatus.NOT_STARTED, 0)
        blocked_features = status_counts.get(FeatureStatus.BLOCKED, 0)
        
        # Overall completion percentage
        total_completion = sum(feature.completion_percentage for feature in self.features.values())
        overall_completion = total_completion / total_features if total_features > 0 else 0
        
        # Completion by category
        category_completion = {}
        category_counts = {}
        for feature in self.features.values():
            if feature.category not in category_completion:
                category_completion[feature.category] = 0
                category_counts[feature.category] = 0
            category_completion[feature.category] += feature.completion_percentage
            category_counts[feature.category] += 1
        
        for category in category_completion:
            category_completion[category] /= category_counts[category]
        
        # Completion by priority
        priority_completion = {}
        priority_counts = {}
        for feature in self.features.values():
            if feature.priority not in priority_completion:
                priority_completion[feature.priority] = 0
                priority_counts[feature.priority] = 0
            priority_completion[feature.priority] += feature.completion_percentage
            priority_counts[feature.priority] += 1
        
        for priority in priority_completion:
            priority_completion[priority] /= priority_counts[priority]
        
        # Time estimates
        estimated_total = sum(feature.estimated_hours or 0 for feature in self.features.values())
        actual_spent = sum(feature.actual_hours or 0 for feature in self.features.values())
        
        # Remaining hours (for incomplete features)
        remaining_estimated = sum(
            (feature.estimated_hours or 0) * (1 - feature.completion_percentage / 100)
            for feature in self.features.values()
            if feature.status != FeatureStatus.COMPLETED
        )
        
        return ProjectMetrics(
            total_features=total_features,
            completed_features=completed_features,
            in_progress_features=in_progress_features,
            not_started_features=not_started_features,
            blocked_features=blocked_features,
            overall_completion_percentage=overall_completion,
            completion_by_category=category_completion,
            completion_by_priority={p.value: v for p, v in priority_completion.items()},
            estimated_total_hours=estimated_total,
            actual_hours_spent=actual_spent,
            remaining_estimated_hours=remaining_estimated
        )
    
    def get_feature_by_id(self, feature_id: str) -> Optional[Feature]:
        """Get a feature by ID."""
        return self.features.get(feature_id)
    
    def get_features_by_category(self, category: str) -> List[Feature]:
        """Get features by category."""
        return [f for f in self.features.values() if f.category == category]
    
    def get_features_by_status(self, status: FeatureStatus) -> List[Feature]:
        """Get features by status."""
        return [f for f in self.features.values() if f.status == status]
    
    def get_features_by_priority(self, priority: Priority) -> List[Feature]:
        """Get features by priority."""
        return [f for f in self.features.values() if f.priority == priority]
    
    def update_feature(self, feature_id: str, **updates) -> bool:
        """Update a feature."""
        if feature_id not in self.features:
            return False
        
        feature = self.features[feature_id]
        
        # Update fields
        for field, value in updates.items():
            if hasattr(feature, field):
                setattr(feature, field, value)
        
        # Update timestamp
        feature.last_updated = datetime.now()
        
        # Auto-set completion date if status is completed
        if feature.status == FeatureStatus.COMPLETED and not feature.completed_at:
            feature.completed_at = datetime.now()
        
        # Auto-set start date if status changes from not_started
        if feature.status != FeatureStatus.NOT_STARTED and not feature.started_at:
            feature.started_at = datetime.now()
        
        self._save_features()
        return True
    
    def add_feature(self, feature: Feature) -> bool:
        """Add a new feature."""
        if feature.id in self.features:
            return False
        
        self.features[feature.id] = feature
        self._save_features()
        return True
    
    def generate_todo_list(self, priority_filter: Optional[Priority] = None,
                          category_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """Generate a prioritized todo list."""
        features = list(self.features.values())
        
        # Apply filters
        if priority_filter:
            features = [f for f in features if f.priority == priority_filter]
        
        if category_filter:
            features = [f for f in features if f.category == category_filter]
        
        # Filter out completed features
        features = [f for f in features if f.status != FeatureStatus.COMPLETED]
        
        # Sort by priority and completion percentage
        priority_order = {Priority.CRITICAL: 0, Priority.HIGH: 1, Priority.MEDIUM: 2, Priority.LOW: 3}
        features.sort(key=lambda x: (priority_order[x.priority], -x.completion_percentage))
        
        todo_items = []
        for feature in features:
            todo_items.append({
                'id': feature.id,
                'name': feature.name,
                'description': feature.description,
                'category': feature.category,
                'status': feature.status.value,
                'priority': feature.priority.value,
                'completion_percentage': feature.completion_percentage,
                'estimated_hours': feature.estimated_hours,
                'depends_on': feature.depends_on,
                'known_issues': feature.known_issues
            })
        
        return todo_items

# Global feature audit instance
feature_audit = FeatureAudit()
