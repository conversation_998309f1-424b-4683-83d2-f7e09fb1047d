"""
Google Drive Sync Utility for JobFlow
Handles secure backup and sync of user data to Google Drive.
"""

import json
import os
import io
from datetime import datetime
from typing import Dict, Any, Optional, List
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload, MediaIoBaseUpload
import streamlit as st
from cryptography.fernet import Fernet
import base64

class GoogleDriveSync:
    """Handles Google Drive synchronization for JobFlow data."""
    
    def __init__(self, account_manager):
        self.account_manager = account_manager
        self.scopes = ['https://www.googleapis.com/auth/drive.file']
        self.app_folder_name = 'JobFlow_Backup'
        
        # OAuth2 configuration (you'll need to set these up in Google Cloud Console)
        self.client_config = {
            "web": {
                "client_id": os.getenv("GOOGLE_CLIENT_ID"),
                "client_secret": os.getenv("GOOGLE_CLIENT_SECRET"),
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": ["http://localhost:8501"]
            }
        }
    
    def get_authorization_url(self) -> str:
        """Get Google OAuth2 authorization URL."""
        flow = Flow.from_client_config(
            self.client_config,
            scopes=self.scopes,
            redirect_uri="http://localhost:8501"
        )
        
        auth_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true'
        )
        
        return auth_url
    
    def exchange_code_for_tokens(self, authorization_code: str) -> Optional[Dict[str, Any]]:
        """Exchange authorization code for access tokens."""
        try:
            flow = Flow.from_client_config(
                self.client_config,
                scopes=self.scopes,
                redirect_uri="http://localhost:8501"
            )
            
            flow.fetch_token(code=authorization_code)
            credentials = flow.credentials
            
            return {
                'token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'token_uri': credentials.token_uri,
                'client_id': credentials.client_id,
                'client_secret': credentials.client_secret,
                'scopes': credentials.scopes
            }
        except Exception as e:
            st.error(f"Failed to exchange authorization code: {str(e)}")
            return None
    
    def get_drive_service(self, user_id: str):
        """Get authenticated Google Drive service."""
        # Load stored credentials
        creds_data = self.account_manager.load_credentials(user_id, 'google_drive')
        if not creds_data:
            return None
        
        credentials = Credentials(
            token=creds_data['token'],
            refresh_token=creds_data.get('refresh_token'),
            token_uri=creds_data['token_uri'],
            client_id=creds_data['client_id'],
            client_secret=creds_data['client_secret'],
            scopes=creds_data['scopes']
        )
        
        # Refresh token if needed
        if credentials.expired and credentials.refresh_token:
            credentials.refresh(Request())
            
            # Update stored credentials
            updated_creds = {
                'token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'token_uri': credentials.token_uri,
                'client_id': credentials.client_id,
                'client_secret': credentials.client_secret,
                'scopes': credentials.scopes
            }
            self.account_manager.save_credentials(user_id, 'google_drive', updated_creds)
        
        return build('drive', 'v3', credentials=credentials)
    
    def create_app_folder(self, service) -> str:
        """Create or get JobFlow app folder in Google Drive."""
        # Search for existing folder
        results = service.files().list(
            q=f"name='{self.app_folder_name}' and mimeType='application/vnd.google-apps.folder'",
            fields="files(id, name)"
        ).execute()
        
        folders = results.get('files', [])
        
        if folders:
            return folders[0]['id']
        
        # Create new folder
        folder_metadata = {
            'name': self.app_folder_name,
            'mimeType': 'application/vnd.google-apps.folder'
        }
        
        folder = service.files().create(body=folder_metadata, fields='id').execute()
        return folder.get('id')
    
    def encrypt_data(self, data: Dict[str, Any], password: str) -> bytes:
        """Encrypt data with user password."""
        # Generate key from password
        key = base64.urlsafe_b64encode(password.encode().ljust(32)[:32])
        cipher = Fernet(key)
        
        # Encrypt data
        json_data = json.dumps(data, indent=2)
        encrypted_data = cipher.encrypt(json_data.encode())
        
        return encrypted_data
    
    def decrypt_data(self, encrypted_data: bytes, password: str) -> Optional[Dict[str, Any]]:
        """Decrypt data with user password."""
        try:
            # Generate key from password
            key = base64.urlsafe_b64encode(password.encode().ljust(32)[:32])
            cipher = Fernet(key)
            
            # Decrypt data
            decrypted_data = cipher.decrypt(encrypted_data)
            data = json.loads(decrypted_data.decode())
            
            return data
        except Exception as e:
            st.error(f"Failed to decrypt data: {str(e)}")
            return None
    
    def backup_to_drive(self, user_id: str, password: str) -> bool:
        """Backup user data to Google Drive."""
        try:
            service = self.get_drive_service(user_id)
            if not service:
                st.error("Google Drive not connected")
                return False
            
            # Get app folder
            folder_id = self.create_app_folder(service)
            
            # Export user data
            user_data = self.account_manager.export_user_data(user_id, include_credentials=True)
            
            # Add metadata
            backup_data = {
                'user_data': user_data,
                'backup_created': datetime.now().isoformat(),
                'app_version': '1.0',
                'encrypted': True
            }
            
            # Encrypt data
            encrypted_data = self.encrypt_data(backup_data, password)
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"jobflow_backup_{timestamp}.encrypted"
            
            # Upload to Drive
            media = MediaIoBaseUpload(
                io.BytesIO(encrypted_data),
                mimetype='application/octet-stream'
            )
            
            file_metadata = {
                'name': filename,
                'parents': [folder_id]
            }
            
            file = service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()
            
            st.success(f"Backup created successfully: {filename}")
            return True
            
        except Exception as e:
            st.error(f"Backup failed: {str(e)}")
            return False
    
    def list_backups(self, user_id: str) -> List[Dict[str, Any]]:
        """List available backups from Google Drive."""
        try:
            service = self.get_drive_service(user_id)
            if not service:
                return []
            
            # Get app folder
            folder_id = self.create_app_folder(service)
            
            # List backup files
            results = service.files().list(
                q=f"'{folder_id}' in parents and name contains 'jobflow_backup_'",
                fields="files(id, name, createdTime, size)",
                orderBy="createdTime desc"
            ).execute()
            
            files = results.get('files', [])
            
            backups = []
            for file in files:
                backups.append({
                    'id': file['id'],
                    'name': file['name'],
                    'created': file['createdTime'],
                    'size': file.get('size', 0)
                })
            
            return backups
            
        except Exception as e:
            st.error(f"Failed to list backups: {str(e)}")
            return []
    
    def restore_from_drive(self, user_id: str, backup_id: str, password: str) -> bool:
        """Restore user data from Google Drive backup."""
        try:
            service = self.get_drive_service(user_id)
            if not service:
                st.error("Google Drive not connected")
                return False
            
            # Download backup file
            request = service.files().get_media(fileId=backup_id)
            file_io = io.BytesIO()
            downloader = MediaIoBaseDownload(file_io, request)
            
            done = False
            while done is False:
                status, done = downloader.next_chunk()
            
            # Get encrypted data
            encrypted_data = file_io.getvalue()
            
            # Decrypt data
            backup_data = self.decrypt_data(encrypted_data, password)
            if not backup_data:
                return False
            
            # Restore user data
            user_data = backup_data['user_data']
            
            # Update account
            if 'account' in user_data:
                self.account_manager.update_account(user_id, user_data['account'])
            
            # Restore cached jobs
            if 'jobs' in user_data:
                for job in user_data['jobs']:
                    self.account_manager.cache_job_data(user_id, job)
            
            st.success("Data restored successfully from backup")
            return True
            
        except Exception as e:
            st.error(f"Restore failed: {str(e)}")
            return False
    
    def setup_auto_sync(self, user_id: str, enabled: bool) -> bool:
        """Enable or disable automatic sync."""
        try:
            account = self.account_manager.get_account(user_id)
            if account:
                account['settings']['google_drive_sync'] = enabled
                account['settings']['auto_sync_enabled'] = enabled
                account['settings']['last_sync'] = datetime.now().isoformat() if enabled else None
                
                return self.account_manager.update_account(user_id, account)
            
            return False
        except Exception as e:
            st.error(f"Failed to setup auto sync: {str(e)}")
            return False
