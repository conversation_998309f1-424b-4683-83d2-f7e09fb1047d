"""
Simple Google Sign-In Component for JobFlow
One-click Google authentication without complex OAuth setup.
"""

import streamlit as st
import streamlit.components.v1 as components
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from utils.account_manager import AccountManager
from styles.theme import COLORS, TYPOGRAPHY

class GoogleSignIn:
    """Simple Google Sign-In component using Google Identity Services."""
    
    def __init__(self, account_manager: AccountManager):
        self.account_manager = account_manager
        self.client_id = os.getenv("GOOGLE_CLIENT_ID")
        
    def render_sign_in_button(self) -> Optional[Dict[str, Any]]:
        """Render Google Sign-In button and handle authentication."""
        
        if not self.client_id:
            st.warning("⚠️ Google Client ID not configured. Using local accounts only.")
            return None
        
        # Google Sign-In HTML/JavaScript
        google_signin_html = f"""
        <div id="google-signin-container" style="
            display: flex;
            justify-content: center;
            margin: 1rem 0;
        ">
            <div id="g_id_onload"
                 data-client_id="{self.client_id}"
                 data-context="signin"
                 data-ux_mode="popup"
                 data-callback="handleCredentialResponse"
                 data-auto_prompt="false">
            </div>
            
            <div class="g_id_signin"
                 data-type="standard"
                 data-shape="rectangular"
                 data-theme="filled_blue"
                 data-text="signin_with"
                 data-size="large"
                 data-logo_alignment="left">
            </div>
        </div>
        
        <script src="https://accounts.google.com/gsi/client" async defer></script>
        
        <script>
        function handleCredentialResponse(response) {{
            // Decode the JWT token to get user info
            const responsePayload = decodeJwtResponse(response.credential);
            
            // Send user data to Streamlit
            const userData = {{
                email: responsePayload.email,
                name: responsePayload.name,
                picture: responsePayload.picture,
                google_id: responsePayload.sub,
                credential: response.credential
            }};
            
            // Store in session storage for Streamlit to pick up
            sessionStorage.setItem('google_user_data', JSON.stringify(userData));
            
            // Trigger Streamlit rerun
            window.parent.postMessage({{
                type: 'streamlit:setComponentValue',
                value: userData
            }}, '*');
        }}
        
        function decodeJwtResponse(token) {{
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {{
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }}).join(''));
            return JSON.parse(jsonPayload);
        }}
        </script>
        
        <style>
        .g_id_signin {{
            margin: 0 auto;
        }}
        
        #google-signin-container {{
            background: {COLORS['surface']};
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid {COLORS['border']};
            margin: 1rem 0;
        }}
        </style>
        """
        
        # Render the component
        user_data = components.html(
            google_signin_html,
            height=120
        )
        
        # Check for user data from session storage
        if user_data:
            return self._process_google_user(user_data)
        
        # Also check session storage directly
        if 'google_user_data' not in st.session_state:
            # Try to get from browser session storage
            check_session_html = """
            <script>
            const userData = sessionStorage.getItem('google_user_data');
            if (userData) {
                const parsed = JSON.parse(userData);
                window.parent.postMessage({
                    type: 'streamlit:setComponentValue',
                    value: parsed
                }, '*');
                sessionStorage.removeItem('google_user_data');
            }
            </script>
            """
            components.html(check_session_html, height=0)
        
        return None
    
    def _process_google_user(self, user_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process Google user data and create/login account."""
        try:
            # Handle both dict and other data types
            if isinstance(user_data, dict):
                email = user_data.get('email')
                name = user_data.get('name')
                google_id = user_data.get('google_id')
                picture = user_data.get('picture')
            else:
                # If user_data is not a dict, it might be a component return value
                st.warning("Received unexpected data format from Google authentication")
                return None

            if not email or not name:
                st.error("Failed to get user information from Google")
                return None
            
            # Check if account already exists
            accounts = self.account_manager._load_accounts()
            existing_account = None
            
            for account in accounts.values():
                if account.get('email') == email or account.get('google_id') == google_id:
                    existing_account = account
                    break
            
            if existing_account:
                # Login existing user
                existing_account['last_login'] = datetime.now().isoformat()
                self.account_manager._save_accounts(accounts)
                
                st.success(f"✅ Welcome back, {name}!")
                return existing_account
            else:
                # Create new account
                username = email.split('@')[0]  # Use email prefix as username
                
                # Ensure username is unique
                counter = 1
                original_username = username
                while any(acc.get('username') == username for acc in accounts.values()):
                    username = f"{original_username}{counter}"
                    counter += 1
                
                # Create account with Google info
                account_data = {
                    'user_id': google_id,
                    'username': username,
                    'email': email,
                    'name': name,
                    'picture': picture,
                    'google_id': google_id,
                    'auth_provider': 'google',
                    'created_at': datetime.now().isoformat(),
                    'last_login': datetime.now().isoformat(),
                    'settings': {
                        'theme': 'dark',
                        'notifications': True,
                        'auto_save': True,
                        'google_drive_sync': False,
                    },
                    'credentials': {},
                    'progress': {
                        'onboarding_completed': False,
                        'credentials_setup': False,
                        'first_job_search': False,
                    }
                }
                
                # Save account
                accounts[google_id] = account_data
                self.account_manager._save_accounts(accounts)
                
                # Create user directories
                user_dir = self.account_manager.cache_dir / google_id
                user_dir.mkdir(exist_ok=True)
                (user_dir / "jobs").mkdir(exist_ok=True)
                (user_dir / "searches").mkdir(exist_ok=True)
                (user_dir / "exports").mkdir(exist_ok=True)
                
                st.success(f"✅ Welcome to JobFlow, {name}!")
                return account_data
                
        except Exception as e:
            st.error(f"Failed to process Google authentication: {str(e)}")
            return None
    
    def render_google_option_card(self) -> None:
        """Render a card explaining Google sign-in option."""
        if not self.client_id:
            st.info("⚠️ Google Client ID not configured. Using local accounts only.")
            return

        # Use Streamlit native components instead of raw HTML
        with st.container():
            st.markdown("### 🚀 Quick Start with Google")
            st.write("Sign in with your Google account for instant setup. We'll automatically create your JobFlow profile and you can optionally enable Google Drive backup later.")

            # Create three columns for the features
            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown("⚡")
                st.markdown("**Instant Setup**")
                st.caption("No forms to fill")

            with col2:
                st.markdown("🔒")
                st.markdown("**Secure**")
                st.caption("Google handles auth")

            with col3:
                st.markdown("☁️")
                st.markdown("**Optional Backup**")
                st.caption("Enable Drive sync later")


def get_google_setup_instructions() -> str:
    """Get simple setup instructions for Google Client ID."""
    return """
    ## 🔧 Simple Google Setup (Optional)
    
    To enable "Sign in with Google":
    
    1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
    2. **Create a new project** (or select existing)
    3. **Enable Google Identity** (it's usually enabled by default)
    4. **Create credentials:**
       - Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client ID"
       - Application type: "Web application"
       - Authorized origins: `http://localhost:8501`
    5. **Copy the Client ID** and add to your `.env` file:
       ```
       GOOGLE_CLIENT_ID=your_client_id_here.apps.googleusercontent.com
       ```
    
    **That's it!** No client secret or complex setup needed for basic authentication.
    
    💡 **Note**: This is optional. Users can still create local accounts without Google setup.
    """
