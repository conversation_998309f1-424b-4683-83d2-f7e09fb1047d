"""
Google Drive Setup Component
Beautiful UI for Google Drive integration and backup setup.
"""

import streamlit as st
import streamlit_shadcn_ui as ui
from typing import Dict, Any, Optional
from datetime import datetime
from utils.google_drive_sync import GoogleDriveSync
from utils.account_manager import AccountManager
from styles.theme import COLORS, TYPOGRAPHY

class GoogleDriveSetupComponent:
    """Component for setting up Google Drive integration."""
    
    def __init__(self, account_manager: AccountManager):
        self.account_manager = account_manager
        self.google_sync = GoogleDriveSync(account_manager)
    
    def render_setup(self, user_id: str) -> bool:
        """Render Google Drive setup interface."""
        st.markdown(f"""
        <div style="text-align: center; margin-bottom: 2rem;">
            <h2 style="
                color: {COLORS['text_primary']};
                font-size: {TYPOGRAPHY['font_sizes']['2xl']};
                margin-bottom: 0.5rem;
            ">☁️ Google Drive Backup</h2>
            <p style="
                color: {COLORS['text_secondary']};
                font-size: {TYPOGRAPHY['font_sizes']['base']};
            ">Secure backup and sync your JobFlow data across devices</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Check if already connected
        existing_creds = self.account_manager.load_credentials(user_id, 'google_drive')
        
        if existing_creds:
            return self._render_connected_state(user_id)
        else:
            return self._render_setup_state(user_id)
    
    def _render_setup_state(self, user_id: str) -> bool:
        """Render initial setup state."""
        # Benefits section
        col1, col2 = st.columns(2)
        
        with col1:
            ui.card(
                content=f"""
                ### 🔒 Secure Backup
                
                - **Encrypted Storage**: Your data is encrypted before upload
                - **Private Access**: Only you can access your backup files
                - **Version History**: Keep multiple backup versions
                - **Easy Restore**: One-click data restoration
                """,
                key="backup_benefits"
            )
        
        with col2:
            ui.card(
                content=f"""
                ### 🌐 Cross-Device Sync
                
                - **Multiple Devices**: Access from any device
                - **Real-time Sync**: Automatic background sync
                - **Offline Access**: Local data always available
                - **Export Options**: Download your data anytime
                """,
                key="sync_benefits"
            )
        
        st.markdown("<br>", unsafe_allow_html=True)
        
        # Setup process
        st.subheader("🚀 Setup Process")
        
        steps_html = f"""
        <div style="
            background: {COLORS['surface']};
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid {COLORS['border']};
        ">
        """
        
        steps = [
            "Click 'Connect Google Drive' below",
            "Sign in to your Google account",
            "Grant JobFlow access to create backup files",
            "Choose your backup password",
            "Start automatic syncing!"
        ]
        
        for i, step in enumerate(steps, 1):
            steps_html += f"""
            <div style="
                display: flex;
                align-items: center;
                margin-bottom: 1rem;
                padding: 0.5rem;
                background: {COLORS['elevated']};
                border-radius: 8px;
            ">
                <div style="
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    background: {COLORS['accent']};
                    color: {COLORS['text_primary']};
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 600;
                    margin-right: 1rem;
                ">
                    {i}
                </div>
                <span style="color: {COLORS['text_primary']};">{step}</span>
            </div>
            """
        
        steps_html += "</div>"
        st.markdown(steps_html, unsafe_allow_html=True)
        
        # Connect button
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🔗 Connect Google Drive", key="connect_drive", use_container_width=True, type="primary"):
                self._start_oauth_flow(user_id)
        
        # Skip option
        st.markdown("<br>", unsafe_allow_html=True)
        st.info("💡 **Optional**: You can skip this step and set up Google Drive sync later in Settings.")
        
        return False
    
    def _render_connected_state(self, user_id: str) -> bool:
        """Render connected state with management options."""
        st.success("✅ Google Drive is connected!")
        
        # Connection info
        account = self.account_manager.get_account(user_id)
        last_sync = account.get('settings', {}).get('last_sync')
        
        col1, col2 = st.columns(2)
        
        with col1:
            ui.card(
                content=f"""
                ### 📊 Sync Status
                
                **Status**: Connected ✅
                **Last Sync**: {self._format_last_sync(last_sync)}
                **Auto Sync**: {'Enabled' if account.get('settings', {}).get('auto_sync_enabled', False) else 'Disabled'}
                **Backup Folder**: JobFlow_Backup
                """,
                key="sync_status"
            )
        
        with col2:
            ui.card(
                content=f"""
                ### 🛠️ Quick Actions
                
                - Create manual backup
                - View backup history
                - Download backup file
                - Restore from backup
                """,
                key="quick_actions"
            )
        
        st.markdown("<br>", unsafe_allow_html=True)
        
        # Action buttons
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("💾 Backup Now", key="backup_now", use_container_width=True):
                self._create_backup(user_id)
        
        with col2:
            if st.button("📋 View Backups", key="view_backups", use_container_width=True):
                self._show_backup_list(user_id)
        
        with col3:
            if st.button("🔄 Restore Data", key="restore_data", use_container_width=True):
                self._show_restore_options(user_id)
        
        with col4:
            if st.button("⚙️ Settings", key="drive_settings", use_container_width=True):
                self._show_drive_settings(user_id)
        
        return True
    
    def _start_oauth_flow(self, user_id: str):
        """Start Google OAuth flow."""
        try:
            auth_url = self.google_sync.get_authorization_url()
            
            st.markdown(f"""
            <div style="
                background: {COLORS['info']};
                color: {COLORS['text_primary']};
                padding: 1rem;
                border-radius: 8px;
                margin: 1rem 0;
            ">
                <h4>🔗 Connect to Google Drive</h4>
                <p>Click the link below to authorize JobFlow to access your Google Drive:</p>
                <a href="{auth_url}" target="_blank" style="
                    color: {COLORS['text_primary']};
                    text-decoration: underline;
                    font-weight: 600;
                ">Authorize JobFlow</a>
            </div>
            """, unsafe_allow_html=True)
            
            # Authorization code input
            auth_code = st.text_input(
                "Authorization Code",
                placeholder="Paste the authorization code here",
                help="After authorizing, copy the code from the redirect page"
            )
            
            if st.button("Complete Setup", disabled=not auth_code):
                if auth_code:
                    tokens = self.google_sync.exchange_code_for_tokens(auth_code)
                    if tokens:
                        # Save credentials
                        if self.account_manager.save_credentials(user_id, 'google_drive', tokens):
                            st.success("✅ Google Drive connected successfully!")
                            st.rerun()
                        else:
                            st.error("Failed to save Google Drive credentials")
                    else:
                        st.error("Invalid authorization code")
        
        except Exception as e:
            st.error(f"Failed to start OAuth flow: {str(e)}")
    
    def _create_backup(self, user_id: str):
        """Create a manual backup."""
        password = st.text_input(
            "Backup Password",
            type="password",
            placeholder="Enter a password to encrypt your backup",
            help="This password will be used to encrypt your backup file"
        )
        
        if password and st.button("Create Backup"):
            with st.spinner("Creating backup..."):
                if self.google_sync.backup_to_drive(user_id, password):
                    st.success("✅ Backup created successfully!")
                else:
                    st.error("❌ Backup failed")
    
    def _show_backup_list(self, user_id: str):
        """Show list of available backups."""
        with st.spinner("Loading backups..."):
            backups = self.google_sync.list_backups(user_id)
        
        if backups:
            st.subheader("📋 Available Backups")
            
            for backup in backups:
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    st.write(f"**{backup['name']}**")
                    st.caption(f"Created: {backup['created']}")
                
                with col2:
                    st.write(f"Size: {self._format_file_size(int(backup['size']))}")
                
                with col3:
                    if st.button("Restore", key=f"restore_{backup['id']}"):
                        st.session_state.restore_backup_id = backup['id']
                        st.rerun()
        else:
            st.info("No backups found")
    
    def _show_restore_options(self, user_id: str):
        """Show restore options."""
        if 'restore_backup_id' in st.session_state:
            backup_id = st.session_state.restore_backup_id
            
            st.warning("⚠️ Restoring will overwrite your current data!")
            
            password = st.text_input(
                "Backup Password",
                type="password",
                placeholder="Enter the password used to encrypt this backup"
            )
            
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("Cancel", key="cancel_restore"):
                    del st.session_state.restore_backup_id
                    st.rerun()
            
            with col2:
                if st.button("Restore Data", key="confirm_restore", type="primary"):
                    if password:
                        with st.spinner("Restoring data..."):
                            if self.google_sync.restore_from_drive(user_id, backup_id, password):
                                st.success("✅ Data restored successfully!")
                                del st.session_state.restore_backup_id
                                st.rerun()
                            else:
                                st.error("❌ Restore failed")
                    else:
                        st.error("Please enter the backup password")
    
    def _show_drive_settings(self, user_id: str):
        """Show Google Drive settings."""
        st.subheader("⚙️ Google Drive Settings")
        
        account = self.account_manager.get_account(user_id)
        current_settings = account.get('settings', {})
        
        auto_sync = st.checkbox(
            "Enable Automatic Sync",
            value=current_settings.get('auto_sync_enabled', False),
            help="Automatically backup your data to Google Drive"
        )
        
        if st.button("Save Settings"):
            updates = {
                'settings': {
                    **current_settings,
                    'auto_sync_enabled': auto_sync
                }
            }
            
            if self.account_manager.update_account(user_id, updates):
                st.success("✅ Settings saved!")
            else:
                st.error("❌ Failed to save settings")
    
    def _format_last_sync(self, last_sync: Optional[str]) -> str:
        """Format last sync time."""
        if not last_sync:
            return "Never"
        
        try:
            sync_time = datetime.fromisoformat(last_sync)
            now = datetime.now()
            diff = now - sync_time
            
            if diff.days > 0:
                return f"{diff.days} days ago"
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f"{hours} hours ago"
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f"{minutes} minutes ago"
            else:
                return "Just now"
        except:
            return "Unknown"
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
