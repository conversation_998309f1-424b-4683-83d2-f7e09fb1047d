"""
Authentication system for JobFlow.
Handles user authentication, session management, and security.
"""

import hashlib
import secrets
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import streamlit as st
from pydantic import BaseModel, Field

from database.manager import db_manager
from utils.config import config_manager
from utils.logger import get_logger

logger = get_logger(__name__)

class UserSession(BaseModel):
    """Model for user sessions."""
    session_id: str
    user_email: str
    created_at: datetime = Field(default_factory=datetime.now)
    last_activity: datetime = Field(default_factory=datetime.now)
    expires_at: datetime
    is_active: bool = True
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class AuthenticationManager:
    """Manages user authentication and sessions."""
    
    def __init__(self):
        self.session_timeout = 3600  # 1 hour in seconds
        self.max_login_attempts = 5
        self.lockout_duration = 900  # 15 minutes in seconds
        self.failed_attempts = {}  # Track failed login attempts
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> tuple[str, str]:
        """Hash a password with salt."""
        if salt is None:
            salt = secrets.token_hex(32)
        
        # Use PBKDF2 for password hashing
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 100,000 iterations
        )
        
        return password_hash.hex(), salt
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """Verify a password against its hash."""
        computed_hash, _ = self.hash_password(password, salt)
        return secrets.compare_digest(computed_hash, password_hash)
    
    def create_session(self, user_email: str, ip_address: Optional[str] = None, 
                      user_agent: Optional[str] = None) -> str:
        """Create a new user session."""
        session_id = secrets.token_urlsafe(32)
        expires_at = datetime.now() + timedelta(seconds=self.session_timeout)
        
        session = UserSession(
            session_id=session_id,
            user_email=user_email,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        # Store session in Streamlit session state
        if 'user_sessions' not in st.session_state:
            st.session_state.user_sessions = {}
        
        st.session_state.user_sessions[session_id] = session
        
        logger.info(f"Created session for user: {user_email}")
        return session_id
    
    def validate_session(self, session_id: str) -> Optional[UserSession]:
        """Validate and return a session if valid."""
        if 'user_sessions' not in st.session_state:
            return None
        
        session = st.session_state.user_sessions.get(session_id)
        if not session:
            return None
        
        # Check if session is expired
        if datetime.now() > session.expires_at:
            self.destroy_session(session_id)
            return None
        
        # Update last activity
        session.last_activity = datetime.now()
        return session
    
    def destroy_session(self, session_id: str) -> bool:
        """Destroy a user session."""
        if 'user_sessions' not in st.session_state:
            return False
        
        if session_id in st.session_state.user_sessions:
            session = st.session_state.user_sessions[session_id]
            del st.session_state.user_sessions[session_id]
            logger.info(f"Destroyed session for user: {session.user_email}")
            return True
        
        return False
    
    def is_account_locked(self, email: str) -> bool:
        """Check if an account is locked due to failed login attempts."""
        if email not in self.failed_attempts:
            return False
        
        attempts_data = self.failed_attempts[email]
        
        # Check if lockout period has expired
        if time.time() - attempts_data['last_attempt'] > self.lockout_duration:
            del self.failed_attempts[email]
            return False
        
        return attempts_data['count'] >= self.max_login_attempts
    
    def record_failed_attempt(self, email: str):
        """Record a failed login attempt."""
        current_time = time.time()
        
        if email not in self.failed_attempts:
            self.failed_attempts[email] = {'count': 0, 'last_attempt': current_time}
        
        # Reset count if last attempt was more than lockout duration ago
        if current_time - self.failed_attempts[email]['last_attempt'] > self.lockout_duration:
            self.failed_attempts[email]['count'] = 0
        
        self.failed_attempts[email]['count'] += 1
        self.failed_attempts[email]['last_attempt'] = current_time
        
        logger.warning(f"Failed login attempt for {email}. Count: {self.failed_attempts[email]['count']}")
    
    def clear_failed_attempts(self, email: str):
        """Clear failed login attempts for a user."""
        if email in self.failed_attempts:
            del self.failed_attempts[email]
    
    def authenticate_user(self, email: str, password: str) -> Optional[str]:
        """Authenticate a user and return session ID if successful."""
        try:
            # Check if account is locked
            if self.is_account_locked(email):
                logger.warning(f"Login attempt for locked account: {email}")
                return None
            
            # For now, use simple authentication (in production, use proper user database)
            # This is a placeholder - in real implementation, you'd check against a user database
            if self._verify_user_credentials(email, password):
                # Clear failed attempts on successful login
                self.clear_failed_attempts(email)
                
                # Create session
                session_id = self.create_session(email)
                return session_id
            else:
                # Record failed attempt
                self.record_failed_attempt(email)
                return None
                
        except Exception as e:
            logger.error(f"Authentication error for {email}: {str(e)}")
            return None
    
    def _verify_user_credentials(self, email: str, password: str) -> bool:
        """Verify user credentials (placeholder implementation)."""
        # This is a simple placeholder implementation
        # In production, you would:
        # 1. Query user database for the email
        # 2. Verify password hash
        # 3. Check if account is active
        
        # For demo purposes, accept any email with password "jobflow123"
        demo_password = "jobflow123"
        return password == demo_password
    
    def require_authentication(self) -> Optional[str]:
        """Require authentication for the current session."""
        # Check if user is already authenticated
        if 'session_id' in st.session_state:
            session = self.validate_session(st.session_state.session_id)
            if session:
                return session.user_email
        
        # Show login form
        return self._show_login_form()
    
    def _show_login_form(self) -> Optional[str]:
        """Show login form and handle authentication."""
        st.markdown("### 🔐 Login Required")
        
        with st.form("login_form"):
            email = st.text_input("Email", placeholder="<EMAIL>")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            
            col1, col2 = st.columns(2)
            with col1:
                login_button = st.form_submit_button("🔑 Login", type="primary")
            with col2:
                demo_button = st.form_submit_button("🎮 Demo Mode")
            
            if demo_button:
                # Demo mode - skip authentication
                st.session_state.session_id = self.create_session("<EMAIL>")
                st.session_state.demo_mode = True
                st.success("✅ Logged in as demo user!")
                st.rerun()
                return "<EMAIL>"
            
            if login_button:
                if not email or not password:
                    st.error("Please enter both email and password.")
                    return None
                
                # Check if account is locked
                if self.is_account_locked(email):
                    st.error(f"🔒 Account locked due to too many failed attempts. Try again in {self.lockout_duration // 60} minutes.")
                    return None
                
                # Attempt authentication
                session_id = self.authenticate_user(email, password)
                
                if session_id:
                    st.session_state.session_id = session_id
                    st.success("✅ Login successful!")
                    st.rerun()
                    return email
                else:
                    attempts_left = self.max_login_attempts - self.failed_attempts.get(email, {}).get('count', 0)
                    if attempts_left > 0:
                        st.error(f"❌ Invalid credentials. {attempts_left} attempts remaining.")
                    else:
                        st.error("🔒 Account locked due to too many failed attempts.")
                    return None
        
        # Show demo instructions
        st.info("""
        **Demo Mode**: Click "Demo Mode" to try the application without authentication.
        
        **Login**: Use any email with password `jobflow123` for testing.
        
        **Features**:
        - Session management with automatic timeout
        - Account lockout after failed attempts
        - Secure password hashing (in production)
        """)
        
        return None
    
    def logout(self):
        """Logout the current user."""
        if 'session_id' in st.session_state:
            self.destroy_session(st.session_state.session_id)
            del st.session_state.session_id
        
        if 'demo_mode' in st.session_state:
            del st.session_state.demo_mode
        
        # Clear other session data
        for key in list(st.session_state.keys()):
            if key.startswith('user_'):
                del st.session_state[key]
        
        st.success("✅ Logged out successfully!")
        st.rerun()
    
    def get_session_info(self) -> Optional[Dict[str, Any]]:
        """Get current session information."""
        if 'session_id' not in st.session_state:
            return None
        
        session = self.validate_session(st.session_state.session_id)
        if not session:
            return None
        
        return {
            'user_email': session.user_email,
            'created_at': session.created_at,
            'last_activity': session.last_activity,
            'expires_at': session.expires_at,
            'is_demo': st.session_state.get('demo_mode', False)
        }
    
    def extend_session(self, session_id: str) -> bool:
        """Extend a session's expiration time."""
        session = self.validate_session(session_id)
        if session:
            session.expires_at = datetime.now() + timedelta(seconds=self.session_timeout)
            return True
        return False

# Global authentication manager
auth_manager = AuthenticationManager()
