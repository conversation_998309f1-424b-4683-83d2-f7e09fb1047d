"""
LinkedIn job portal automation.
Handles job searching and application submission on LinkedIn.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

from automation.browser import JobPortalAutomation
from models import JobPortalName
from utils.logger import get_logger

logger = get_logger(__name__)

class LinkedInAutomation(JobPortalAutomation):
    """LinkedIn-specific automation implementation."""
    
    def __init__(self):
        super().__init__(JobPortalName.LINKEDIN)
        self.base_url = "https://www.linkedin.com"
        self.jobs_url = "https://www.linkedin.com/jobs"
    
    async def _perform_login(self, email: str, password: str) -> bool:
        """Perform LinkedIn login."""
        try:
            # Navigate to login page
            await self.page.goto(f"{self.base_url}/login")
            await self.page.wait_for_load_state('networkidle')
            
            # Fill login form
            email_selector = '#username'
            password_selector = '#password'
            login_button = 'button[type="submit"]'
            
            if not await self.wait_for_element(email_selector):
                logger.error("Email field not found")
                return False
            
            await self.safe_fill(email_selector, email)
            await self.safe_fill(password_selector, password)
            
            # Click login button
            await self.safe_click(login_button)
            
            # Wait for redirect and check if login was successful
            await self.page.wait_for_timeout(3000)
            
            # Check if we're on the feed page (successful login)
            current_url = self.page.url
            if 'feed' in current_url or 'mynetwork' in current_url:
                logger.info("LinkedIn login successful")
                return True
            
            # Check for 2FA or other challenges
            if await self.page.query_selector('[data-test-id="challenge"]'):
                logger.warning("LinkedIn 2FA challenge detected - manual intervention required")
                return False
            
            logger.error("LinkedIn login failed - unknown error")
            return False
            
        except Exception as e:
            logger.error(f"LinkedIn login error: {str(e)}")
            return False
    
    async def _perform_job_search(self, keywords: List[str], location: str = "") -> List[Dict[str, Any]]:
        """Perform LinkedIn job search."""
        try:
            jobs = []
            search_query = " ".join(keywords)
            
            # Navigate to jobs page
            await self.page.goto(self.jobs_url)
            await self.page.wait_for_load_state('networkidle')
            
            # Fill search form
            keywords_input = 'input[aria-label="Search by title, skill, or company"]'
            location_input = 'input[aria-label="City, state, or zip code"]'
            search_button = 'button[aria-label="Search"]'
            
            if not await self.wait_for_element(keywords_input):
                logger.error("LinkedIn search form not found")
                return []
            
            # Clear and fill search fields
            await self.safe_fill(keywords_input, search_query)
            if location:
                await self.safe_fill(location_input, location)
            
            # Click search
            await self.safe_click(search_button)
            await self.page.wait_for_load_state('networkidle')
            
            # Apply filters for remote/easy apply jobs
            await self._apply_search_filters()
            
            # Extract job listings
            jobs = await self._extract_job_listings()
            
            logger.info(f"Found {len(jobs)} jobs on LinkedIn")
            return jobs
            
        except Exception as e:
            logger.error(f"LinkedIn job search error: {str(e)}")
            return []
    
    async def _apply_search_filters(self):
        """Apply search filters for better job matching."""
        try:
            # Click on filters button
            filters_button = 'button[aria-label="Show all filters"]'
            if await self.wait_for_element(filters_button, timeout=5000):
                await self.safe_click(filters_button)
                await self.page.wait_for_timeout(2000)
                
                # Apply Easy Apply filter
                easy_apply_checkbox = 'input[id*="easy-apply"]'
                if await self.wait_for_element(easy_apply_checkbox, timeout=3000):
                    await self.safe_click(easy_apply_checkbox)
                
                # Apply Remote work filter
                remote_checkbox = 'input[value="2"]'  # Remote work type
                if await self.wait_for_element(remote_checkbox, timeout=3000):
                    await self.safe_click(remote_checkbox)
                
                # Apply filters
                apply_button = 'button[aria-label="Apply current filters"]'
                if await self.wait_for_element(apply_button, timeout=3000):
                    await self.safe_click(apply_button)
                    await self.page.wait_for_load_state('networkidle')
                
        except Exception as e:
            logger.warning(f"Could not apply LinkedIn filters: {str(e)}")
    
    async def _extract_job_listings(self) -> List[Dict[str, Any]]:
        """Extract job listings from search results."""
        jobs = []
        
        try:
            # Wait for job listings to load
            job_cards_selector = '.job-search-card'
            await self.page.wait_for_selector(job_cards_selector, timeout=10000)
            
            # Get all job cards
            job_cards = await self.page.query_selector_all(job_cards_selector)
            
            for i, card in enumerate(job_cards[:20]):  # Limit to first 20 jobs
                try:
                    job_data = await self._extract_job_data(card)
                    if job_data:
                        jobs.append(job_data)
                except Exception as e:
                    logger.warning(f"Error extracting job {i}: {str(e)}")
                    continue
            
        except Exception as e:
            logger.error(f"Error extracting LinkedIn job listings: {str(e)}")
        
        return jobs
    
    async def _extract_job_data(self, card) -> Optional[Dict[str, Any]]:
        """Extract data from a single job card."""
        try:
            # Extract job title
            title_element = await card.query_selector('.job-search-card__title a')
            title = await title_element.inner_text() if title_element else "Unknown Title"
            
            # Extract company name
            company_element = await card.query_selector('.job-search-card__subtitle a')
            company = await company_element.inner_text() if company_element else "Unknown Company"
            
            # Extract location
            location_element = await card.query_selector('.job-search-card__location')
            location = await location_element.inner_text() if location_element else "Unknown Location"
            
            # Extract job URL
            link_element = await card.query_selector('.job-search-card__title a')
            job_url = await link_element.get_attribute('href') if link_element else ""
            if job_url and not job_url.startswith('http'):
                job_url = f"{self.base_url}{job_url}"
            
            # Check if it's Easy Apply
            easy_apply = await card.query_selector('.job-search-card__easy-apply-button') is not None
            
            # Extract posted date
            posted_element = await card.query_selector('.job-search-card__listdate')
            posted_date = await posted_element.inner_text() if posted_element else "Unknown"
            
            return {
                'title': title.strip(),
                'company': company.strip(),
                'location': location.strip(),
                'url': job_url,
                'posted_date': posted_date.strip(),
                'portal': 'LinkedIn',
                'easy_apply': easy_apply,
                'description': '',  # Will be filled when viewing individual job
                'salary': '',  # LinkedIn often doesn't show salary in listings
                'job_type': 'Full-time',  # Default assumption
                'extracted_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error extracting job data: {str(e)}")
            return None
    
    async def _perform_job_application(self, job_data: Dict[str, Any], 
                                     resume_path: str, cover_letter: str) -> bool:
        """Perform LinkedIn Easy Apply job application."""
        try:
            job_url = job_data.get('url', '')
            if not job_url:
                logger.error("No job URL provided for LinkedIn application")
                return False
            
            # Navigate to job page
            await self.page.goto(job_url)
            await self.page.wait_for_load_state('networkidle')
            
            # Check if Easy Apply is available
            easy_apply_button = '.jobs-apply-button--top-card button'
            if not await self.wait_for_element(easy_apply_button, timeout=5000):
                logger.warning("Easy Apply button not found - job may not support Easy Apply")
                return False
            
            # Click Easy Apply
            await self.safe_click(easy_apply_button)
            await self.page.wait_for_timeout(2000)
            
            # Handle the Easy Apply flow
            success = await self._handle_easy_apply_flow(resume_path, cover_letter)
            
            if success:
                logger.info(f"Successfully applied to {job_data.get('title', 'Unknown')} at {job_data.get('company', 'Unknown')}")
            
            return success
            
        except Exception as e:
            logger.error(f"LinkedIn job application error: {str(e)}")
            return False
    
    async def _handle_easy_apply_flow(self, resume_path: str, cover_letter: str) -> bool:
        """Handle the LinkedIn Easy Apply multi-step flow."""
        try:
            max_steps = 5  # LinkedIn Easy Apply usually has 2-4 steps
            current_step = 0
            
            while current_step < max_steps:
                current_step += 1
                
                # Check if we're on a form step
                if await self.page.query_selector('.jobs-easy-apply-content'):
                    
                    # Handle resume upload if file input is present
                    file_input = 'input[type="file"]'
                    if await self.wait_for_element(file_input, timeout=2000):
                        await self.upload_file(file_input, resume_path)
                        await self.page.wait_for_timeout(2000)
                    
                    # Handle cover letter if text area is present
                    cover_letter_textarea = 'textarea[id*="cover-letter"], textarea[name*="cover"]'
                    if await self.wait_for_element(cover_letter_textarea, timeout=2000):
                        await self.safe_fill(cover_letter_textarea, cover_letter)
                    
                    # Handle additional questions (basic text inputs)
                    text_inputs = await self.page.query_selector_all('input[type="text"]:not([disabled])')
                    for input_field in text_inputs:
                        placeholder = await input_field.get_attribute('placeholder')
                        if placeholder and 'years' in placeholder.lower():
                            await input_field.fill('3')  # Default years of experience
                    
                    # Look for Next/Continue button
                    next_button = 'button[aria-label*="Continue"], button[aria-label*="Next"], button:has-text("Next")'
                    if await self.wait_for_element(next_button, timeout=3000):
                        await self.safe_click(next_button)
                        await self.page.wait_for_timeout(2000)
                        continue
                    
                    # Look for Submit/Send application button
                    submit_button = 'button[aria-label*="Submit"], button[aria-label*="Send"], button:has-text("Submit")'
                    if await self.wait_for_element(submit_button, timeout=3000):
                        await self.safe_click(submit_button)
                        await self.page.wait_for_timeout(3000)
                        
                        # Check for success confirmation
                        success_indicator = '.jobs-easy-apply-confirmation, [data-test-modal-id="easy-apply-success"]'
                        if await self.wait_for_element(success_indicator, timeout=5000):
                            logger.info("LinkedIn Easy Apply submission successful")
                            return True
                        
                        break
                
                # If no form elements found, we might be done or stuck
                break
            
            logger.warning("LinkedIn Easy Apply flow completed but success not confirmed")
            return False
            
        except Exception as e:
            logger.error(f"LinkedIn Easy Apply flow error: {str(e)}")
            return False
    
    async def get_job_description(self, job_url: str) -> str:
        """Get full job description from job page."""
        try:
            await self.page.goto(job_url)
            await self.page.wait_for_load_state('networkidle')
            
            # Extract job description
            description_selector = '.jobs-description-content__text, .jobs-box__html-content'
            if await self.wait_for_element(description_selector):
                description_element = await self.page.query_selector(description_selector)
                description = await description_element.inner_text()
                return description.strip()
            
            return ""
            
        except Exception as e:
            logger.error(f"Error getting LinkedIn job description: {str(e)}")
            return ""
