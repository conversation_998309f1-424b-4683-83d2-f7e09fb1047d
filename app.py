"""
JobFlow - Modern Job Application Automation System
A beautiful, privacy-focused job application automation platform with AI-powered CV processing,
real-time cost monitoring, and pet-lover friendly design.
"""

import streamlit as st
import streamlit_antd_components as sac
import streamlit_shadcn_ui as ui
from streamlit_option_menu import option_menu
import extra_streamlit_components as stx
import asyncio
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

# Import our modules
from models import (
    UserProfile, JobApplication, ApplicationSettings, SystemStatus,
    JobPortalName, ApplicationStatus, ExperienceLevel
)
from database.manager import db_manager
from components.onboarding import OnboardingFlow
from components.credentials import CredentialManager
from components.dashboard import Dashboard
from automation.api_client import api_manager
from utils.config import config_manager
from utils.logger import get_logger

# Import new AI and design modules
from styles.theme import apply_theme, get_color, COLORS
from ai.openrouter_client import openrouter_client
from ai.cv_processor import cv_processor
from ai.cost_tracker import CostTracker
from automation.queue_manager import queue_manager
from auth.authentication import auth_manager

# Configure page
st.set_page_config(
    page_title="JobFlow - Smart Job Application Automation",
    page_icon="🐾",  # Pet-friendly icon
    layout="wide",
    initial_sidebar_state="expanded"
)

# Apply custom theme immediately
apply_theme()

# Force dark theme with additional CSS
st.markdown(f"""
<style>
    /* Force dark background for all elements */
    .stApp, .main, .block-container {{
        background-color: {COLORS['primary']} !important;
        color: {COLORS['white']} !important;
    }}

    /* Override any light theme remnants */
    .css-1d391kg, .css-18e3th9, .css-1y4p8pa {{
        background-color: {COLORS['primary']} !important;
    }}

    /* Ensure text is white */
    * {{
        color: {COLORS['white']} !important;
    }}

    /* Exception for accent colored elements */
    .stButton > button, .accent-text {{
        color: {COLORS['white']} !important;
    }}
</style>
""", unsafe_allow_html=True)

# Enhanced CSS for modern styling
st.markdown("""
<style>
    /* Main theme colors */
    :root {
        --primary-color: #667eea;
        --secondary-color: #764ba2;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
    }

    /* Header styling */
    .main-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }

    /* Card components */
    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border-left: 5px solid var(--primary-color);
        margin-bottom: 1.5rem;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    /* Metric cards */
    .metric-card {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    /* Status indicators */
    .status-success { color: var(--success-color); font-weight: bold; }
    .status-warning { color: var(--warning-color); font-weight: bold; }
    .status-danger { color: var(--danger-color); font-weight: bold; }
    .status-info { color: var(--info-color); font-weight: bold; }

    /* Button styling */
    .stButton > button {
        background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    }

    /* Sidebar styling */
    .css-1d391kg {
        background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    }

    /* Data tables */
    .dataframe {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    /* Progress bars */
    .stProgress > div > div > div > div {
        background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    }
</style>
""", unsafe_allow_html=True)

# Initialize logger
logger = get_logger(__name__)


def init_session_state():
    """Initialize session state variables with modern state management."""
    # Core application state
    if 'app_initialized' not in st.session_state:
        st.session_state.app_initialized = False

    # User authentication and profile
    if 'user_email' not in st.session_state:
        st.session_state.user_email = None
    if 'user_profile' not in st.session_state:
        st.session_state.user_profile = None

    # Onboarding state
    if 'onboarding_complete' not in st.session_state:
        st.session_state.onboarding_complete = False

    # Navigation state
    if 'current_page' not in st.session_state:
        st.session_state.current_page = "Dashboard"

    # Application data
    if 'applications' not in st.session_state:
        st.session_state.applications = []
    if 'search_history' not in st.session_state:
        st.session_state.search_history = []

    # Settings and configuration
    if 'settings' not in st.session_state:
        st.session_state.settings = ApplicationSettings()
    if 'system_status' not in st.session_state:
        st.session_state.system_status = SystemStatus()

    # UI state
    if 'show_notifications' not in st.session_state:
        st.session_state.show_notifications = True
    if 'theme_mode' not in st.session_state:
        st.session_state.theme_mode = "light"


def load_user_data():
    """Load user data from database and check onboarding status."""
    try:
        # Check if onboarding is complete
        onboarding_file = config_manager.get_data_dir() / "onboarding_complete.json"
        if onboarding_file.exists():
            st.session_state.onboarding_complete = True

            # Load the user email from onboarding data
            import json
            with open(onboarding_file, 'r') as f:
                onboarding_data = json.load(f)
                # Try to get user email from a separate file or database
                user_file = config_manager.get_data_dir() / "current_user.json"
                if user_file.exists():
                    with open(user_file, 'r') as uf:
                        user_data = json.load(uf)
                        st.session_state.user_email = user_data.get('email')

        # Load user profile from database if we have an email
        if st.session_state.user_email:
            profile = db_manager.get_user_profile(st.session_state.user_email)
            if profile:
                st.session_state.user_profile = profile

                # Load recent applications
                applications = db_manager.get_job_applications(st.session_state.user_email, limit=50)
                st.session_state.applications = applications

                logger.info(f"Loaded user data for {st.session_state.user_email}")
            else:
                logger.warning(f"No profile found for {st.session_state.user_email}")

    except Exception as e:
        logger.error(f"Error loading user data: {e}")
        # Reset state on error
        st.session_state.onboarding_complete = False
        st.session_state.user_email = None
        st.session_state.user_profile = None


def save_user_data():
    """Save user data to database with Pydantic v2 compatibility."""
    try:
        # Save user profile to database
        if st.session_state.user_profile and st.session_state.user_email:
            db_manager.save_user_profile(st.session_state.user_profile)

            # Save current user email for future sessions
            user_file = config_manager.get_data_dir() / "current_user.json"
            import json
            with open(user_file, 'w') as f:
                json.dump({"email": st.session_state.user_email}, f)

        # Mark onboarding as complete
        if st.session_state.onboarding_complete:
            onboarding_file = config_manager.get_data_dir() / "onboarding_complete.json"
            import json
            with open(onboarding_file, 'w') as f:
                json.dump({"completed": True, "date": datetime.now().isoformat()}, f)

        # Save settings to local file (using model_dump for Pydantic v2)
        if st.session_state.settings:
            settings_file = config_manager.get_data_dir() / "settings.json"
            import json
            with open(settings_file, 'w') as f:
                json.dump(st.session_state.settings.model_dump(), f, indent=2, default=str)

        logger.info("User data saved successfully")

    except Exception as e:
        logger.error(f"Error saving user data: {e}")
        st.error(f"Error saving user data: {e}")


def render_sidebar():
    """Render the modern sidebar navigation with cost monitoring."""
    with st.sidebar:
        # Logo and branding
        st.markdown(f"""
        <div style="text-align: center; padding: 1rem 0; background: linear-gradient(135deg, {COLORS['primary']} 0%, {COLORS['accent']} 100%); border-radius: 12px; margin-bottom: 1rem;">
            <h1 style="color: white; margin: 0; font-family: 'Funnel Sans', sans-serif;">🐾 JobFlow</h1>
            <p style="color: {COLORS['light_gray']}; margin: 0; font-size: 0.9rem;">Your Friendly Job Assistant</p>
        </div>
        """, unsafe_allow_html=True)

        # User profile section
        if st.session_state.user_profile:
            st.markdown(f"""
            <div class="custom-card" style="background: {COLORS['white']}; padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                <h4 style="color: {COLORS['primary']}; margin: 0;">👤 {st.session_state.user_profile.name}</h4>
                <p style="color: {COLORS['medium_gray']}; margin: 0; font-size: 0.9em;">{st.session_state.user_email}</p>
            </div>
            """, unsafe_allow_html=True)

            # AI Cost Monitoring
            try:
                cost_tracker = CostTracker()
                daily_summary = asyncio.run(cost_tracker.get_daily_summary())
                monthly_summary = asyncio.run(cost_tracker.get_monthly_summary())

                st.markdown(f"""
                <div class="custom-card" style="background: {COLORS['light_gray']}; padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                    <h5 style="color: {COLORS['primary']}; margin: 0 0 0.5rem 0;">🤖 AI Usage Today</h5>
                    <p style="margin: 0; font-size: 0.8rem; color: {COLORS['dark_gray']};">
                        Cost: <strong>${daily_summary.get('total_cost', 0):.3f}</strong><br>
                        Requests: <strong>{daily_summary.get('total_requests', 0)}</strong><br>
                        Tokens: <strong>{daily_summary.get('total_tokens', 0):,}</strong>
                    </p>
                    <p style="margin: 0.5rem 0 0 0; font-size: 0.7rem; color: {COLORS['medium_gray']};">
                        Monthly: ${monthly_summary.get('total_cost', 0):.2f}
                    </p>
                </div>
                """, unsafe_allow_html=True)

            except Exception as e:
                logger.error(f"Error displaying cost info: {str(e)}")
                st.markdown(f"""
                <div class="custom-card" style="background: {COLORS['light_gray']}; padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                    <h5 style="color: {COLORS['primary']}; margin: 0;">🤖 AI Usage</h5>
                    <p style="margin: 0; font-size: 0.8rem; color: {COLORS['medium_gray']};">Loading...</p>
                </div>
                """, unsafe_allow_html=True)

        # Navigation menu with new styling
        selected = option_menu(
            menu_title="Navigation",
            options=["Dashboard", "Job Search", "Applications", "Analytics", "AI Assistant", "Project Status", "Settings"],
            icons=["speedometer2", "search", "briefcase", "graph-up", "robot", "clipboard-check", "gear"],
            menu_icon="compass",
            default_index=0 if st.session_state.current_page == "Dashboard" else
                         (1 if st.session_state.current_page == "Job Search" else
                          2 if st.session_state.current_page == "Applications" else
                          3 if st.session_state.current_page == "Analytics" else
                          4 if st.session_state.current_page == "AI Assistant" else
                          5 if st.session_state.current_page == "Project Status" else 6),
            styles={
                "container": {"padding": "0!important", "background-color": "transparent"},
                "icon": {"color": COLORS['accent'], "font-size": "18px"},
                "nav-link": {
                    "font-size": "16px",
                    "text-align": "left",
                    "margin": "2px 0",
                    "padding": "0.75rem 1rem",
                    "color": COLORS['primary'],
                    "background-color": "transparent",
                    "border-radius": "8px",
                    "font-family": "'Funnel Sans', sans-serif",
                },
                "nav-link-selected": {
                    "background-color": COLORS['accent'],
                    "color": "white",
                    "font-weight": "600",
                },
                "nav-link:hover": {
                    "background-color": COLORS['light_gray'],
                },
            }
        )

        # Update current page
        if selected != st.session_state.current_page:
            st.session_state.current_page = selected
            st.rerun()

        # Add logout button at the bottom
        st.markdown("---")

        # Session info
        session_info = auth_manager.get_session_info()
        if session_info:
            if session_info['is_demo']:
                st.markdown("🎮 **Demo Mode**")
            else:
                st.markdown(f"👤 **{session_info['user_email']}**")

            # Logout button
            if st.button("🚪 Logout", use_container_width=True):
                auth_manager.logout()

        return selected


def render_job_search():
    """Render the modern job search page with enhanced UI."""
    st.header("🔍 Smart Job Search")

    if not st.session_state.user_profile:
        ui.alert(
            text="Please complete your profile setup first to enable job search.",
            type="warning",
            closable=True,
            key="profile_warning"
        )
        return

    # Search form with modern UI
    with st.form("job_search_form"):
        st.subheader("🎯 Search Parameters")

        col1, col2 = st.columns(2)
        with col1:
            keywords = st.text_input(
                "🔍 Keywords",
                value="python developer",
                placeholder="e.g., Python Developer, Data Scientist",
                help="Enter job keywords separated by commas"
            )
            location = st.text_input(
                "📍 Location",
                value="Remote",
                placeholder="Remote, New York, San Francisco"
            )

        with col2:
            portals = st.multiselect(
                "🌐 Job Portals",
                options=[portal.value for portal in JobPortalName],
                default=[JobPortalName.WE_WORK_REMOTELY.value],
                help="Select portals to search"
            )
            max_results = st.slider(
                "📊 Max Results per Portal",
                min_value=5, max_value=100, value=20, step=5
            )

        # Advanced filters
        with st.expander("🔧 Advanced Filters", expanded=False):
            col3, col4 = st.columns(2)
            with col3:
                salary_range = st.slider(
                    "💰 Salary Range (K)",
                    min_value=0, max_value=300, value=(50, 150), step=10
                )
                experience_filter = st.selectbox(
                    "📈 Experience Level",
                    options=["Any"] + [level.value for level in ExperienceLevel],
                    index=0
                )
            with col4:
                job_type_filter = st.selectbox(
                    "💼 Employment Type",
                    options=["Any", "Full-time", "Part-time", "Contract", "Freelance"],
                    index=0
                )
                remote_only = st.checkbox("🏠 Remote Only", value=True)

        # Search button
        search_clicked = st.form_submit_button(
            "🚀 Search Jobs",
            use_container_width=True,
            type="primary"
        )

    # Search results section
    if search_clicked:
        if not keywords.strip():
            st.error("Please enter search keywords.")
            return

        # Save search to history
        search_data = {
            'keywords': keywords,
            'location': location,
            'portals': portals,
            'timestamp': datetime.now()
        }
        if 'search_history' not in st.session_state:
            st.session_state.search_history = []
        st.session_state.search_history.insert(0, search_data)

        with st.spinner("🔍 Searching for jobs across portals..."):
            try:
                # Convert portal names back to enum
                selected_portals = [JobPortalName(portal) for portal in portals]
                keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]

                # Search jobs using API
                results = asyncio.run(api_manager.search_all_portals(
                    keywords=keyword_list,
                    location=location,
                    enabled_portals=selected_portals
                ))

                # Display results with modern UI
                total_jobs = sum(len(jobs) for jobs in results.values())

                if total_jobs > 0:
                    st.success(f"✅ Found {total_jobs} jobs across {len(results)} portals!")

                    # Results summary
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        ui.metric_card(
                            title="Total Jobs",
                            content=str(total_jobs),
                            key="total_jobs_metric"
                        )
                    with col2:
                        ui.metric_card(
                            title="Portals",
                            content=str(len([p for p in results.values() if p])),
                            key="portals_metric"
                        )
                    with col3:
                        avg_per_portal = total_jobs / len(results) if results else 0
                        ui.metric_card(
                            title="Avg/Portal",
                            content=f"{avg_per_portal:.1f}",
                            key="avg_portal_metric"
                        )
                    with col4:
                        ui.metric_card(
                            title="New Today",
                            content="0",  # TODO: Implement new jobs detection
                            key="new_jobs_metric"
                        )

                    # Display jobs by portal
                    for portal_name, jobs in results.items():
                        if jobs:
                            st.subheader(f"🌐 {portal_name.value} ({len(jobs)} jobs)")

                            for i, job in enumerate(jobs[:max_results]):
                                with st.expander(f"📋 {job['title']} - {job['company']}", expanded=False):
                                    col1, col2 = st.columns([3, 1])

                                    with col1:
                                        st.markdown(f"**🏢 Company:** {job['company']}")
                                        st.markdown(f"**📍 Location:** {job.get('location', 'Not specified')}")
                                        st.markdown(f"**📅 Posted:** {job.get('posted_date', 'Not specified')}")

                                        if job.get('description'):
                                            st.markdown("**📝 Description:**")
                                            description = job['description'][:400] + "..." if len(job['description']) > 400 else job['description']
                                            st.markdown(description)

                                    with col2:
                                        # CV Optimization button
                                        if st.button("✨ Optimize CV", key=f"optimize_{portal_name.value}_{i}"):
                                            st.session_state[f"show_optimization_{portal_name.value}_{i}"] = True
                                            st.rerun()

                                        if st.button("💼 Apply", key=f"apply_{portal_name.value}_{i}"):
                                            # TODO: Implement application logic
                                            st.info("🚧 Application feature coming soon!")

                                        if st.button("🔗 View Job", key=f"view_{portal_name.value}_{i}"):
                                            st.markdown(f"[🔗 Open Job Posting]({job['url']})")

                                        if st.button("💾 Save", key=f"save_{portal_name.value}_{i}"):
                                            # TODO: Implement save job logic
                                            st.success("Job saved!")

                                # CV Optimization modal
                                if st.session_state.get(f"show_optimization_{portal_name.value}_{i}", False):
                                    st.markdown("---")
                                    st.subheader("✨ CV Optimization")

                                    # Check if user has AI credentials
                                    from utils.cv_optimizer import cv_optimizer
                                    available_providers = cv_optimizer.get_available_providers(st.session_state.user_email)

                                    if not available_providers:
                                        st.warning("⚠️ No AI credentials found. Add OpenAI or Anthropic API keys in the Credentials section to enable CV optimization.")
                                        if st.button("❌ Close", key=f"close_opt_{portal_name.value}_{i}"):
                                            st.session_state[f"show_optimization_{portal_name.value}_{i}"] = False
                                            st.rerun()
                                    else:
                                        # Show optimization options
                                        provider = st.selectbox(
                                            "AI Provider",
                                            options=available_providers,
                                            format_func=lambda x: "OpenAI (GPT-4)" if x == "openai" else "Anthropic (Claude)",
                                            key=f"provider_{portal_name.value}_{i}"
                                        )

                                        col_opt1, col_opt2 = st.columns(2)

                                        with col_opt1:
                                            if st.button("🔍 Analyze Match", key=f"analyze_{portal_name.value}_{i}"):
                                                with st.spinner("Analyzing job match..."):
                                                    # Get CV text from user profile
                                                    cv_text = getattr(st.session_state.user_profile, 'cv_text', '')
                                                    if not cv_text:
                                                        st.error("No CV text found. Please re-upload your CV.")
                                                    else:
                                                        analysis = cv_optimizer.analyze_job_match(
                                                            cv_text, job.get('description', ''),
                                                            st.session_state.user_email, provider
                                                        )

                                                        if analysis:
                                                            st.success(f"**Match Score: {analysis.get('match_score', 0)}%**")

                                                            col_a1, col_a2 = st.columns(2)
                                                            with col_a1:
                                                                st.write("**Matching Skills:**")
                                                                for skill in analysis.get('matching_skills', []):
                                                                    st.write(f"✅ {skill}")

                                                            with col_a2:
                                                                st.write("**Missing Skills:**")
                                                                for skill in analysis.get('missing_skills', []):
                                                                    st.write(f"❌ {skill}")

                                                            st.write("**Recommendations:**")
                                                            for rec in analysis.get('recommendations', []):
                                                                st.write(f"💡 {rec}")

                                        with col_opt2:
                                            if st.button("✨ Optimize CV", key=f"opt_cv_{portal_name.value}_{i}"):
                                                with st.spinner("Optimizing CV for this job..."):
                                                    # Get CV text from user profile
                                                    cv_text = getattr(st.session_state.user_profile, 'cv_text', '')
                                                    if not cv_text:
                                                        st.error("No CV text found. Please re-upload your CV.")
                                                    else:
                                                        optimized_cv = cv_optimizer.optimize_cv_for_job(
                                                            cv_text, job.get('description', ''),
                                                            st.session_state.user_email, provider
                                                        )

                                                        if optimized_cv:
                                                            st.success("✅ CV optimized successfully!")

                                                            # Show download button
                                                            st.download_button(
                                                                label="📥 Download Optimized CV",
                                                                data=optimized_cv,
                                                                file_name=f"optimized_cv_{job['company'].replace(' ', '_')}.txt",
                                                                mime="text/plain"
                                                            )

                                                            # Show preview
                                                            with st.expander("👀 Preview Optimized CV"):
                                                                st.text_area(
                                                                    "Optimized CV Content",
                                                                    value=optimized_cv[:1000] + "..." if len(optimized_cv) > 1000 else optimized_cv,
                                                                    height=200,
                                                                    disabled=True
                                                                )

                                        # Close button
                                        if st.button("❌ Close", key=f"close_opt_{portal_name.value}_{i}"):
                                            st.session_state[f"show_optimization_{portal_name.value}_{i}"] = False
                                            st.rerun()
                else:
                    st.warning("😔 No jobs found matching your criteria. Try different keywords or expand your search.")

            except Exception as e:
                st.error(f"❌ Error searching jobs: {e}")
                logger.error(f"Job search error: {e}")


def render_applications_page():
    """Render the applications tracking page."""
    st.header("📋 Application Tracker")

    if not st.session_state.user_email:
        st.warning("Please complete onboarding to view applications.")
        return

    # Get applications from database
    applications = db_manager.get_job_applications(st.session_state.user_email, limit=100)

    if not applications:
        st.info("📝 No applications yet. Start by searching for jobs!")
        if st.button("🔍 Search Jobs", type="primary"):
            st.session_state.current_page = "Job Search"
            st.rerun()
        return

    # Applications summary
    col1, col2, col3, col4 = st.columns(4)

    status_counts = {}
    for app in applications:
        status_counts[app.status.value] = status_counts.get(app.status.value, 0) + 1

    with col1:
        ui.metric_card(
            title="Total Applications",
            content=str(len(applications)),
            key="total_apps"
        )
    with col2:
        interviews = status_counts.get('interview', 0)
        ui.metric_card(
            title="Interviews",
            content=str(interviews),
            key="interviews"
        )
    with col3:
        offers = status_counts.get('offer', 0)
        ui.metric_card(
            title="Offers",
            content=str(offers),
            key="offers"
        )
    with col4:
        success_rate = (offers / len(applications) * 100) if applications else 0
        ui.metric_card(
            title="Success Rate",
            content=f"{success_rate:.1f}%",
            key="success_rate"
        )

    # Applications table
    st.subheader("📊 Recent Applications")

    # Create DataFrame for display
    data = []
    for app in applications:
        data.append({
            'Position': app.position_title,
            'Company': app.company_name,
            'Portal': app.portal_name.value,
            'Status': app.status.value,
            'Applied': app.submission_timestamp.strftime('%Y-%m-%d') if app.submission_timestamp else 'Not submitted',
            'Created': app.created_at.strftime('%Y-%m-%d')
        })

    df = pd.DataFrame(data)

    # Display with filters
    col1, col2, col3 = st.columns(3)
    with col1:
        status_filter = st.selectbox(
            "Filter by Status",
            options=["All"] + list(set(df['Status'])),
            index=0
        )
    with col2:
        portal_filter = st.selectbox(
            "Filter by Portal",
            options=["All"] + list(set(df['Portal'])),
            index=0
        )
    with col3:
        sort_by = st.selectbox(
            "Sort by",
            options=["Created", "Applied", "Company", "Position"],
            index=0
        )

    # Apply filters
    filtered_df = df.copy()
    if status_filter != "All":
        filtered_df = filtered_df[filtered_df['Status'] == status_filter]
    if portal_filter != "All":
        filtered_df = filtered_df[filtered_df['Portal'] == portal_filter]

    # Sort
    filtered_df = filtered_df.sort_values(by=sort_by, ascending=False)

    # Display table
    st.dataframe(
        filtered_df,
        use_container_width=True,
        hide_index=True,
        column_config={
            'Position': st.column_config.TextColumn('Position', width='large'),
            'Company': st.column_config.TextColumn('Company', width='medium'),
            'Portal': st.column_config.TextColumn('Portal', width='small'),
            'Status': st.column_config.TextColumn('Status', width='small'),
            'Applied': st.column_config.TextColumn('Applied', width='small'),
            'Created': st.column_config.TextColumn('Created', width='small')
        }
    )


def render_ai_assistant_page():
    """Render the AI Assistant page with CV processing and cost monitoring."""
    st.header("🤖 AI Assistant")

    if not st.session_state.user_email:
        st.warning("Please complete onboarding to use AI features.")
        return

    # Check for OpenRouter API key
    api_key = config_manager.get_setting("OPENROUTER_API_KEY")
    if not api_key:
        st.error("🔑 OpenRouter API key not configured. Please add your API key in the settings.")

        with st.expander("🔧 How to get an OpenRouter API key"):
            st.markdown("""
            1. Visit [OpenRouter.ai](https://openrouter.ai)
            2. Sign up for an account
            3. Go to your API Keys section
            4. Create a new API key
            5. Add it to your JobFlow settings

            OpenRouter provides access to multiple AI models including Claude, GPT-4, and more.
            """)
        return

    # Tabs for different AI features
    tab1, tab2, tab3, tab4 = st.tabs(["📄 CV Analysis", "💰 Cost Monitor", "🎯 Job Matching", "📝 Cover Letters"])

    with tab1:
        render_cv_analysis_tab()

    with tab2:
        render_cost_monitor_tab()

    with tab3:
        render_job_matching_tab()

    with tab4:
        render_cover_letter_tab()


def render_cv_analysis_tab():
    """Render the CV analysis tab."""
    st.subheader("📄 CV Analysis & Processing")

    # CV Upload Section
    st.markdown("### Upload Your CV")
    uploaded_file = st.file_uploader(
        "Choose your CV file",
        type=['pdf', 'docx', 'txt'],
        help="Upload your CV in PDF, DOCX, or TXT format for AI analysis"
    )

    if uploaded_file is not None:
        col1, col2 = st.columns([2, 1])

        with col1:
            st.success(f"✅ File uploaded: {uploaded_file.name}")
            st.info("📊 File will be processed in the background while you continue using the app.")

        with col2:
            if st.button("🚀 Start AI Analysis", type="primary"):
                with st.spinner("🔄 Queuing CV for analysis..."):
                    try:
                        job_id = asyncio.run(cv_processor.upload_cv(
                            uploaded_file,
                            st.session_state.user_email
                        ))
                        st.success(f"✅ CV queued for analysis! Job ID: {job_id}")
                        st.session_state.current_cv_job = job_id
                        st.rerun()
                    except Exception as e:
                        st.error(f"❌ Error uploading CV: {str(e)}")

    # Processing Status
    if hasattr(st.session_state, 'current_cv_job'):
        st.markdown("### 📊 Processing Status")

        job_status = cv_processor.get_job_status(st.session_state.current_cv_job)
        if job_status:
            col1, col2, col3 = st.columns(3)

            with col1:
                status_color = {
                    "pending": "🟡",
                    "processing": "🔵",
                    "completed": "🟢",
                    "failed": "🔴"
                }.get(job_status.status, "⚪")
                st.metric("Status", f"{status_color} {job_status.status.title()}")

            with col2:
                st.metric("Progress", f"{job_status.progress * 100:.0f}%")

            with col3:
                if job_status.cost_usd:
                    st.metric("Cost", f"${job_status.cost_usd:.4f}")
                else:
                    st.metric("Cost", "Calculating...")

            # Progress bar
            st.progress(job_status.progress)

            # Show results if completed
            if job_status.status == "completed" and job_status.analysis_result:
                st.markdown("### 🎉 Analysis Results")

                analysis = job_status.analysis_result

                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("#### 🛠️ Skills Identified")
                    for skill in analysis.get('skills', [])[:10]:  # Show top 10
                        st.write(f"• {skill}")

                    st.markdown("#### 💼 Experience Level")
                    st.write(f"**{analysis.get('experience_level', 'Unknown').title()}** ({analysis.get('years_experience', 0)} years)")

                with col2:
                    st.markdown("#### 🎯 Strengths")
                    for strength in analysis.get('strengths', [])[:5]:
                        st.write(f"✅ {strength}")

                    st.markdown("#### 📈 Recommended Job Types")
                    for job_type in analysis.get('recommended_job_types', [])[:5]:
                        st.write(f"🎯 {job_type}")

                # Confidence score
                confidence = analysis.get('confidence_score', 0)
                st.metric("🎯 Analysis Confidence", f"{confidence * 100:.0f}%")

                if confidence < 0.7:
                    st.warning("⚠️ Low confidence score. Consider uploading a clearer CV or different format.")

            elif job_status.status == "failed":
                st.error(f"❌ Analysis failed: {job_status.error_message}")
                if st.button("🔄 Retry Analysis"):
                    # Reset and allow re-upload
                    del st.session_state.current_cv_job
                    st.rerun()


def render_cost_monitor_tab():
    """Render the cost monitoring tab."""
    st.subheader("💰 AI Usage & Cost Monitoring")

    try:
        cost_tracker = CostTracker()

        # Get cost summaries
        daily_summary = asyncio.run(cost_tracker.get_daily_summary())
        monthly_summary = asyncio.run(cost_tracker.get_monthly_summary())
        projections = asyncio.run(cost_tracker.get_cost_projection())

        # Cost overview cards
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            ui.metric_card(
                title="Today's Cost",
                content=f"${daily_summary.get('total_cost', 0):.3f}",
                description=f"{daily_summary.get('total_requests', 0)} requests",
                key="daily_cost_metric"
            )

        with col2:
            ui.metric_card(
                title="Monthly Cost",
                content=f"${monthly_summary.get('total_cost', 0):.2f}",
                description=f"{monthly_summary.get('total_requests', 0)} requests",
                key="monthly_cost_metric"
            )

        with col3:
            ui.metric_card(
                title="Projected Monthly",
                content=f"${projections.get('projected_monthly', 0):.2f}",
                description=f"{projections.get('days_remaining', 0)} days left",
                key="projected_cost_metric"
            )

        with col4:
            ui.metric_card(
                title="Daily Average",
                content=f"${projections.get('daily_average', 0):.3f}",
                description="This month",
                key="avg_cost_metric"
            )

        # Cost breakdown charts
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 🤖 Cost by Model")
            model_costs = monthly_summary.get('by_model', {})
            if model_costs:
                df_models = pd.DataFrame(list(model_costs.items()), columns=['Model', 'Cost'])
                st.bar_chart(df_models.set_index('Model'))
            else:
                st.info("No model usage data yet.")

        with col2:
            st.markdown("#### 📊 Cost by Request Type")
            type_costs = monthly_summary.get('by_type', {})
            if type_costs:
                df_types = pd.DataFrame(list(type_costs.items()), columns=['Type', 'Cost'])
                st.bar_chart(df_types.set_index('Type'))
            else:
                st.info("No request type data yet.")

        # Cost limits and alerts
        st.markdown("#### ⚠️ Cost Limits & Alerts")

        col1, col2 = st.columns(2)

        with col1:
            daily_limit = st.number_input(
                "Daily Limit ($)",
                min_value=0.0,
                max_value=100.0,
                value=10.0,
                step=1.0,
                help="Set daily spending limit for AI features"
            )

        with col2:
            monthly_limit = st.number_input(
                "Monthly Limit ($)",
                min_value=0.0,
                max_value=1000.0,
                value=200.0,
                step=10.0,
                help="Set monthly spending limit for AI features"
            )

        if st.button("💾 Update Limits"):
            asyncio.run(cost_tracker.set_limits(daily_limit, monthly_limit))
            st.success("✅ Cost limits updated!")

        # Recent alerts
        alerts = asyncio.run(cost_tracker.get_recent_alerts(5))
        if alerts:
            st.markdown("#### 🚨 Recent Alerts")
            for alert in alerts:
                alert_color = {
                    "info": "🔵",
                    "warning": "🟡",
                    "critical": "🔴"
                }.get(alert.severity, "⚪")

                st.markdown(f"{alert_color} **{alert.alert_type.replace('_', ' ').title()}**: {alert.message}")

    except Exception as e:
        st.error(f"❌ Error loading cost data: {str(e)}")
        logger.error(f"Cost monitoring error: {str(e)}")


def render_job_matching_tab():
    """Render the job matching tab."""
    st.subheader("🎯 AI-Powered Job Matching")

    # Check if user has CV analysis
    if not hasattr(st.session_state, 'current_cv_job'):
        st.warning("📄 Please upload and analyze your CV first in the CV Analysis tab.")
        return

    # Get CV analysis results
    job_status = cv_processor.get_job_status(st.session_state.current_cv_job)
    if not job_status or job_status.status != "completed":
        st.warning("⏳ Please wait for CV analysis to complete before using job matching.")
        return

    cv_analysis = job_status.analysis_result
    if not cv_analysis:
        st.error("❌ CV analysis results not found. Please re-analyze your CV.")
        return

    st.success("✅ CV analysis found! Ready for job matching.")

    # Job matching interface
    st.markdown("### 🔍 Match Jobs Against Your Profile")

    # Option 1: Paste job description
    with st.expander("📝 Analyze Single Job Posting", expanded=True):
        job_description = st.text_area(
            "Paste Job Description",
            height=200,
            placeholder="Paste the full job description here..."
        )

        col1, col2 = st.columns(2)
        with col1:
            company_name = st.text_input("Company Name", placeholder="e.g., Google")
        with col2:
            position_title = st.text_input("Position Title", placeholder="e.g., Senior Python Developer")

        if st.button("🎯 Analyze Job Match", type="primary"):
            if job_description.strip():
                with st.spinner("🤖 Analyzing job match with AI..."):
                    try:
                        # Create job match analysis
                        match_result = asyncio.run(openrouter_client.match_job(
                            job_description=job_description,
                            cv_analysis=cv_analysis,
                            user_preferences={
                                'company_name': company_name,
                                'position_title': position_title
                            }
                        ))

                        # Display results
                        st.markdown("### 📊 Match Analysis Results")

                        # Match score with visual indicator
                        score = match_result.match_score * 100
                        score_color = "🟢" if score >= 80 else "🟡" if score >= 60 else "🔴"

                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("🎯 Match Score", f"{score:.0f}%", delta=f"{score_color}")
                        with col2:
                            st.metric("✅ Matching Skills", len(match_result.matching_skills))
                        with col3:
                            st.metric("❌ Missing Skills", len(match_result.missing_skills))

                        # Detailed breakdown
                        col1, col2 = st.columns(2)

                        with col1:
                            st.markdown("#### ✅ Your Matching Skills")
                            for skill in match_result.matching_skills:
                                st.write(f"• {skill}")

                            st.markdown("#### 📈 Experience Match")
                            if match_result.experience_match:
                                st.success("✅ Your experience level matches the requirements")
                            else:
                                st.warning("⚠️ Experience level may not fully match")

                        with col2:
                            st.markdown("#### ❌ Skills to Develop")
                            for skill in match_result.missing_skills:
                                st.write(f"• {skill}")

                            st.markdown("#### 💰 Salary & Location")
                            if match_result.salary_match:
                                st.success("✅ Salary expectations align")
                            else:
                                st.info("ℹ️ Salary information not available")

                            if match_result.location_match:
                                st.success("✅ Location preferences match")
                            else:
                                st.info("ℹ️ Location preferences unclear")

                        # Overall recommendation
                        st.markdown("#### 🎯 AI Recommendation")
                        st.info(match_result.overall_recommendation)

                        # Cover letter suggestions
                        if match_result.cover_letter_suggestions:
                            st.markdown("#### 📝 Cover Letter Key Points")
                            for suggestion in match_result.cover_letter_suggestions:
                                st.write(f"• {suggestion}")

                        # Action buttons
                        st.markdown("#### 🚀 Next Steps")
                        col1, col2, col3 = st.columns(3)

                        with col1:
                            if st.button("📝 Generate Cover Letter"):
                                st.session_state.generate_cover_letter_data = {
                                    'job_description': job_description,
                                    'company_name': company_name,
                                    'position_title': position_title,
                                    'cv_analysis': cv_analysis
                                }
                                st.success("✅ Cover letter data saved! Go to Cover Letters tab.")

                        with col2:
                            if st.button("💾 Save Job"):
                                # Save job to database
                                st.success("✅ Job saved to your applications!")

                        with col3:
                            if st.button("🔄 Optimize CV"):
                                st.info("🚧 CV optimization coming soon!")

                    except Exception as e:
                        st.error(f"❌ Error analyzing job match: {str(e)}")
                        logger.error(f"Job matching error: {str(e)}")
            else:
                st.error("Please paste a job description to analyze.")

    # Option 2: Batch job matching
    with st.expander("📊 Batch Job Analysis", expanded=False):
        st.markdown("### 🔍 Analyze Multiple Jobs")
        st.info("🚧 Upload a CSV file with job descriptions for batch analysis (coming soon!)")

        uploaded_jobs = st.file_uploader(
            "Upload Jobs CSV",
            type=['csv'],
            help="CSV should have columns: company, position, description, url"
        )

        if uploaded_jobs:
            st.info("📊 Batch job analysis will be implemented in the next update!")

    # Recent matches
    if 'job_matches' not in st.session_state:
        st.session_state.job_matches = []

    if st.session_state.job_matches:
        st.markdown("### 📈 Recent Job Matches")
        for i, match in enumerate(st.session_state.job_matches[-5:]):  # Show last 5
            with st.expander(f"🎯 {match['position']} at {match['company']} ({match['score']:.0f}% match)"):
                st.write(f"**Match Score:** {match['score']:.0f}%")
                st.write(f"**Date:** {match['date']}")
                st.write(f"**Recommendation:** {match['recommendation'][:200]}...")
    else:
        st.info("💡 Your job match history will appear here after you analyze some jobs.")


def render_cover_letter_tab():
    """Render the cover letter generation tab."""
    st.subheader("📝 AI Cover Letter Generation")

    # Check if user has CV analysis
    if not hasattr(st.session_state, 'current_cv_job'):
        st.warning("📄 Please upload and analyze your CV first in the CV Analysis tab.")
        return

    # Get CV analysis results
    job_status = cv_processor.get_job_status(st.session_state.current_cv_job)
    if not job_status or job_status.status != "completed":
        st.warning("⏳ Please wait for CV analysis to complete before generating cover letters.")
        return

    cv_analysis = job_status.analysis_result
    if not cv_analysis:
        st.error("❌ CV analysis results not found. Please re-analyze your CV.")
        return

    # Check if we have data from job matching
    if hasattr(st.session_state, 'generate_cover_letter_data'):
        data = st.session_state.generate_cover_letter_data
        st.success("✅ Job data loaded from matching analysis!")

        # Pre-fill form with job matching data
        with st.form("cover_letter_form_prefilled"):
            st.markdown("### 📋 Job Information (Pre-filled)")

            company_name = st.text_input("Company Name", value=data.get('company_name', ''))
            position_title = st.text_input("Position Title", value=data.get('position_title', ''))
            job_description = st.text_area(
                "Job Description",
                value=data.get('job_description', ''),
                height=150
            )

            # Cover letter customization
            st.markdown("### ✨ Customization Options")

            col1, col2 = st.columns(2)
            with col1:
                tone = st.selectbox(
                    "Writing Tone",
                    options=["Professional", "Enthusiastic", "Confident", "Friendly"],
                    index=0
                )

                length = st.selectbox(
                    "Cover Letter Length",
                    options=["Concise (3 paragraphs)", "Standard (4 paragraphs)", "Detailed (5 paragraphs)"],
                    index=1
                )

            with col2:
                focus_areas = st.multiselect(
                    "Focus Areas",
                    options=["Technical Skills", "Leadership Experience", "Problem Solving", "Team Collaboration", "Innovation", "Results & Achievements"],
                    default=["Technical Skills", "Results & Achievements"]
                )

                include_salary = st.checkbox("Include Salary Discussion", value=False)

            # Personal template
            personal_template = st.text_area(
                "Personal Template (Optional)",
                placeholder="Add any personal touches, specific experiences, or company research you'd like to include...",
                height=100
            )

            generate_button = st.form_submit_button("🚀 Generate Cover Letter", type="primary")

        # Clear the prefilled data after use
        if generate_button:
            del st.session_state.generate_cover_letter_data
    else:
        # Manual cover letter generation
        with st.form("cover_letter_form_manual"):
            st.markdown("### 📋 Job Information")

            company_name = st.text_input("Company Name", placeholder="e.g., Google")
            position_title = st.text_input("Position Title", placeholder="e.g., Senior Python Developer")
            job_description = st.text_area(
                "Job Description",
                placeholder="Paste the full job description here...",
                height=150
            )

            # Cover letter customization
            st.markdown("### ✨ Customization Options")

            col1, col2 = st.columns(2)
            with col1:
                tone = st.selectbox(
                    "Writing Tone",
                    options=["Professional", "Enthusiastic", "Confident", "Friendly"],
                    index=0
                )

                length = st.selectbox(
                    "Cover Letter Length",
                    options=["Concise (3 paragraphs)", "Standard (4 paragraphs)", "Detailed (5 paragraphs)"],
                    index=1
                )

            with col2:
                focus_areas = st.multiselect(
                    "Focus Areas",
                    options=["Technical Skills", "Leadership Experience", "Problem Solving", "Team Collaboration", "Innovation", "Results & Achievements"],
                    default=["Technical Skills", "Results & Achievements"]
                )

                include_salary = st.checkbox("Include Salary Discussion", value=False)

            # Personal template
            personal_template = st.text_area(
                "Personal Template (Optional)",
                placeholder="Add any personal touches, specific experiences, or company research you'd like to include...",
                height=100
            )

            generate_button = st.form_submit_button("🚀 Generate Cover Letter", type="primary")

    # Generate cover letter
    if generate_button:
        if not company_name.strip() or not position_title.strip() or not job_description.strip():
            st.error("Please fill in all required fields (Company Name, Position Title, Job Description).")
        else:
            with st.spinner("🤖 Generating personalized cover letter..."):
                try:
                    # Prepare user template with customizations
                    user_template = f"""
                    Tone: {tone}
                    Length: {length}
                    Focus Areas: {', '.join(focus_areas)}
                    Include Salary Discussion: {include_salary}

                    Personal Notes: {personal_template}
                    """

                    # Generate cover letter
                    cover_letter = asyncio.run(openrouter_client.generate_cover_letter(
                        job_description=job_description,
                        cv_analysis=cv_analysis,
                        company_name=company_name,
                        position_title=position_title,
                        user_template=user_template
                    ))

                    # Display results
                    st.markdown("### 📝 Generated Cover Letter")

                    # Cover letter preview
                    st.markdown("#### 👀 Preview")
                    st.text_area(
                        "Cover Letter Content",
                        value=cover_letter,
                        height=400,
                        key="generated_cover_letter"
                    )

                    # Action buttons
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.download_button(
                            label="📥 Download as TXT",
                            data=cover_letter,
                            file_name=f"cover_letter_{company_name.replace(' ', '_')}_{position_title.replace(' ', '_')}.txt",
                            mime="text/plain"
                        )

                    with col2:
                        # Create a simple HTML version
                        html_cover_letter = cover_letter.replace('\n', '<br>')
                        st.download_button(
                            label="📄 Download as HTML",
                            data=f"<html><body><pre>{html_cover_letter}</pre></body></html>",
                            file_name=f"cover_letter_{company_name.replace(' ', '_')}_{position_title.replace(' ', '_')}.html",
                            mime="text/html"
                        )

                    with col3:
                        if st.button("🔄 Regenerate"):
                            st.rerun()

                    with col4:
                        if st.button("💾 Save to Library"):
                            # Save to cover letter library
                            if 'cover_letter_library' not in st.session_state:
                                st.session_state.cover_letter_library = []

                            st.session_state.cover_letter_library.append({
                                'company': company_name,
                                'position': position_title,
                                'content': cover_letter,
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'tone': tone,
                                'length': length
                            })
                            st.success("✅ Cover letter saved to library!")

                    # Quality metrics
                    st.markdown("#### 📊 Quality Metrics")
                    col1, col2, col3, col4 = st.columns(4)

                    word_count = len(cover_letter.split())
                    char_count = len(cover_letter)
                    paragraph_count = len([p for p in cover_letter.split('\n\n') if p.strip()])

                    with col1:
                        st.metric("Word Count", word_count)
                    with col2:
                        st.metric("Characters", char_count)
                    with col3:
                        st.metric("Paragraphs", paragraph_count)
                    with col4:
                        # Estimate reading time (average 200 words per minute)
                        reading_time = max(1, word_count // 200)
                        st.metric("Reading Time", f"{reading_time} min")

                    # Tips for improvement
                    st.markdown("#### 💡 Tips for Enhancement")
                    st.info("""
                    **Before sending:**
                    - Proofread for any company-specific details
                    - Add specific examples from your experience
                    - Research the company's recent news or achievements
                    - Customize the greeting if you know the hiring manager's name
                    - Ensure the tone matches the company culture
                    """)

                except Exception as e:
                    st.error(f"❌ Error generating cover letter: {str(e)}")
                    logger.error(f"Cover letter generation error: {str(e)}")

    # Cover letter library
    if 'cover_letter_library' in st.session_state and st.session_state.cover_letter_library:
        st.markdown("### 📚 Your Cover Letter Library")

        for i, letter in enumerate(st.session_state.cover_letter_library):
            with st.expander(f"📝 {letter['position']} at {letter['company']} ({letter['date']})"):
                col1, col2 = st.columns([3, 1])

                with col1:
                    st.text_area(
                        "Content Preview",
                        value=letter['content'][:300] + "..." if len(letter['content']) > 300 else letter['content'],
                        height=150,
                        disabled=True,
                        key=f"library_preview_{i}"
                    )

                with col2:
                    st.write(f"**Tone:** {letter['tone']}")
                    st.write(f"**Length:** {letter['length']}")
                    st.write(f"**Words:** {len(letter['content'].split())}")

                    if st.button(f"📥 Download", key=f"download_library_{i}"):
                        st.download_button(
                            label="Download TXT",
                            data=letter['content'],
                            file_name=f"cover_letter_{letter['company']}_{letter['position']}.txt",
                            mime="text/plain",
                            key=f"download_btn_{i}"
                        )

                    if st.button(f"🗑️ Delete", key=f"delete_library_{i}"):
                        st.session_state.cover_letter_library.pop(i)
                        st.rerun()
    else:
        st.info("📚 Your generated cover letters will appear in the library below.")


def render_project_status_page():
    """Render the project status and feature audit page."""
    st.header("📊 Project Status & Feature Audit")

    # Import feature audit
    from utils.feature_audit import feature_audit

    # Get project metrics
    metrics = feature_audit.get_project_metrics()

    # Overview metrics
    st.subheader("🎯 Project Overview")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        ui.metric_card(
            title="Overall Progress",
            content=f"{metrics.overall_completion_percentage:.1f}%",
            description=f"{metrics.completed_features}/{metrics.total_features} features",
            key="overall_progress_metric"
        )

    with col2:
        ui.metric_card(
            title="In Progress",
            content=str(metrics.in_progress_features),
            description="Active features",
            key="in_progress_metric"
        )

    with col3:
        ui.metric_card(
            title="Not Started",
            content=str(metrics.not_started_features),
            description="Pending features",
            key="not_started_metric"
        )

    with col4:
        ui.metric_card(
            title="Estimated Hours",
            content=f"{metrics.remaining_estimated_hours:.1f}h",
            description="Remaining work",
            key="remaining_hours_metric"
        )

    # Progress by category
    st.subheader("📂 Progress by Category")

    category_data = []
    for category, completion in metrics.completion_by_category.items():
        category_data.append({
            'Category': category,
            'Completion': f"{completion:.1f}%",
            'Progress': completion
        })

    df_categories = pd.DataFrame(category_data)

    col1, col2 = st.columns([2, 1])

    with col1:
        st.bar_chart(df_categories.set_index('Category')['Progress'])

    with col2:
        st.dataframe(
            df_categories[['Category', 'Completion']],
            use_container_width=True,
            hide_index=True
        )

    # Priority breakdown
    st.subheader("🔥 Progress by Priority")

    priority_data = []
    for priority, completion in metrics.completion_by_priority.items():
        priority_data.append({
            'Priority': priority.title(),
            'Completion': f"{completion:.1f}%",
            'Progress': completion
        })

    df_priorities = pd.DataFrame(priority_data)
    st.bar_chart(df_priorities.set_index('Priority')['Progress'])

    # Feature details tabs
    st.subheader("🔍 Feature Details")

    tab1, tab2, tab3, tab4 = st.tabs(["📋 Todo List", "✅ Completed", "🚧 In Progress", "📊 All Features"])

    with tab1:
        render_todo_list_tab(feature_audit)

    with tab2:
        render_completed_features_tab(feature_audit)

    with tab3:
        render_in_progress_features_tab(feature_audit)

    with tab4:
        render_all_features_tab(feature_audit)


def render_todo_list_tab(feature_audit):
    """Render the todo list tab."""
    st.markdown("#### 📋 Prioritized Todo List")

    # Filters
    col1, col2 = st.columns(2)

    with col1:
        priority_filter = st.selectbox(
            "Filter by Priority",
            options=["All", "Critical", "High", "Medium", "Low"],
            index=0
        )

    with col2:
        category_filter = st.selectbox(
            "Filter by Category",
            options=["All"] + list(set(f.category for f in feature_audit.features.values())),
            index=0
        )

    # Generate todo list
    from utils.feature_audit import Priority

    priority_map = {
        "Critical": Priority.CRITICAL,
        "High": Priority.HIGH,
        "Medium": Priority.MEDIUM,
        "Low": Priority.LOW
    }

    todo_items = feature_audit.generate_todo_list(
        priority_filter=priority_map.get(priority_filter),
        category_filter=category_filter if category_filter != "All" else None
    )

    if not todo_items:
        st.info("🎉 No pending items! All features in this filter are completed.")
        return

    # Display todo items
    for i, item in enumerate(todo_items):
        with st.expander(f"{'🔥' if item['priority'] == 'critical' else '⚡' if item['priority'] == 'high' else '📌'} {item['name']}", expanded=i < 3):

            col1, col2 = st.columns([3, 1])

            with col1:
                st.markdown(f"**Description:** {item['description']}")
                st.markdown(f"**Category:** {item['category']}")
                st.markdown(f"**Status:** {item['status'].replace('_', ' ').title()}")

                if item['depends_on']:
                    st.markdown(f"**Dependencies:** {', '.join(item['depends_on'])}")

                if item['known_issues']:
                    st.markdown("**Known Issues:**")
                    for issue in item['known_issues']:
                        st.markdown(f"• {issue}")

            with col2:
                st.metric("Priority", item['priority'].title())
                st.metric("Progress", f"{item['completion_percentage']:.0f}%")
                if item['estimated_hours']:
                    st.metric("Est. Hours", f"{item['estimated_hours']:.1f}h")


def render_completed_features_tab(feature_audit):
    """Render the completed features tab."""
    from utils.feature_audit import FeatureStatus

    completed_features = feature_audit.get_features_by_status(FeatureStatus.COMPLETED)

    st.markdown(f"#### ✅ Completed Features ({len(completed_features)})")

    if not completed_features:
        st.info("No completed features yet.")
        return

    # Group by category
    by_category = {}
    for feature in completed_features:
        if feature.category not in by_category:
            by_category[feature.category] = []
        by_category[feature.category].append(feature)

    for category, features in by_category.items():
        st.markdown(f"**{category} ({len(features)} features)**")

        for feature in features:
            with st.expander(f"✅ {feature.name}", expanded=False):
                st.markdown(f"**Description:** {feature.description}")
                st.markdown(f"**Completed:** {feature.completed_at.strftime('%Y-%m-%d') if feature.completed_at else 'Unknown'}")
                if feature.implementation_notes:
                    st.markdown(f"**Notes:** {feature.implementation_notes}")


def render_in_progress_features_tab(feature_audit):
    """Render the in-progress features tab."""
    from utils.feature_audit import FeatureStatus

    in_progress_features = feature_audit.get_features_by_status(FeatureStatus.IN_PROGRESS)

    st.markdown(f"#### 🚧 In Progress Features ({len(in_progress_features)})")

    if not in_progress_features:
        st.info("No features currently in progress.")
        return

    for feature in in_progress_features:
        with st.expander(f"🚧 {feature.name} ({feature.completion_percentage:.0f}%)", expanded=True):

            col1, col2 = st.columns([3, 1])

            with col1:
                st.markdown(f"**Description:** {feature.description}")
                st.markdown(f"**Category:** {feature.category}")

                if feature.implementation_notes:
                    st.markdown(f"**Implementation Notes:** {feature.implementation_notes}")

                if feature.known_issues:
                    st.markdown("**Known Issues:**")
                    for issue in feature.known_issues:
                        st.markdown(f"• {issue}")

            with col2:
                st.metric("Progress", f"{feature.completion_percentage:.0f}%")
                st.metric("Priority", feature.priority.value.title())

                if feature.estimated_hours:
                    remaining_hours = feature.estimated_hours * (1 - feature.completion_percentage / 100)
                    st.metric("Est. Remaining", f"{remaining_hours:.1f}h")

            # Progress bar
            st.progress(feature.completion_percentage / 100)


def render_all_features_tab(feature_audit):
    """Render all features tab."""
    st.markdown("#### 📊 All Features")

    # Create DataFrame
    features_data = []
    for feature in feature_audit.features.values():
        features_data.append({
            'Name': feature.name,
            'Category': feature.category,
            'Status': feature.status.value.replace('_', ' ').title(),
            'Priority': feature.priority.value.title(),
            'Progress': f"{feature.completion_percentage:.0f}%",
            'Est. Hours': feature.estimated_hours or 0,
            'Last Updated': feature.last_updated.strftime('%Y-%m-%d')
        })

    df_features = pd.DataFrame(features_data)

    # Filters
    col1, col2, col3 = st.columns(3)

    with col1:
        status_filter = st.selectbox(
            "Filter by Status",
            options=["All"] + list(df_features['Status'].unique()),
            index=0,
            key="all_features_status_filter"
        )

    with col2:
        category_filter = st.selectbox(
            "Filter by Category",
            options=["All"] + list(df_features['Category'].unique()),
            index=0,
            key="all_features_category_filter"
        )

    with col3:
        priority_filter = st.selectbox(
            "Filter by Priority",
            options=["All"] + list(df_features['Priority'].unique()),
            index=0,
            key="all_features_priority_filter"
        )

    # Apply filters
    filtered_df = df_features.copy()

    if status_filter != "All":
        filtered_df = filtered_df[filtered_df['Status'] == status_filter]

    if category_filter != "All":
        filtered_df = filtered_df[filtered_df['Category'] == category_filter]

    if priority_filter != "All":
        filtered_df = filtered_df[filtered_df['Priority'] == priority_filter]

    # Display table
    st.dataframe(
        filtered_df,
        use_container_width=True,
        hide_index=True,
        column_config={
            'Name': st.column_config.TextColumn('Feature Name', width='large'),
            'Category': st.column_config.TextColumn('Category', width='medium'),
            'Status': st.column_config.TextColumn('Status', width='small'),
            'Priority': st.column_config.TextColumn('Priority', width='small'),
            'Progress': st.column_config.TextColumn('Progress', width='small'),
            'Est. Hours': st.column_config.NumberColumn('Est. Hours', width='small'),
            'Last Updated': st.column_config.TextColumn('Last Updated', width='medium')
        }
    )
def main():
    """Main application function with modern architecture."""
    # Initialize session state
    init_session_state()

    # Initialize database on first run
    if not st.session_state.app_initialized:
        try:
            # Initialize database
            db_manager.init_database()
            st.session_state.app_initialized = True
            logger.info("Application initialized successfully")
        except Exception as e:
            st.error(f"Failed to initialize application: {e}")
            logger.error(f"App initialization failed: {e}")
            return

    # Check authentication first
    user_email = auth_manager.require_authentication()
    if not user_email:
        return  # Authentication form is shown

    # Store authenticated user email
    st.session_state.user_email = user_email

    # Load user data on startup
    if 'data_loaded' not in st.session_state:
        load_user_data()
        st.session_state.data_loaded = True

    # Check if onboarding is complete
    if not st.session_state.onboarding_complete:
        # Render onboarding flow
        onboarding = OnboardingFlow()
        completed = onboarding.render()

        if completed:
            # Onboarding completed, reload the app
            st.rerun()
        return

    # Render sidebar navigation
    current_page = render_sidebar()

    # Render main content based on selected page
    try:
        if current_page == "Dashboard":
            if st.session_state.user_email:
                dashboard = Dashboard(st.session_state.user_email)
                dashboard.render()
            else:
                st.error("User email not found. Please complete onboarding again.")

        elif current_page == "Job Search":
            render_job_search()

        elif current_page == "Applications":
            render_applications_page()

        elif current_page == "Analytics":
            if st.session_state.user_email:
                dashboard = Dashboard(st.session_state.user_email)
                dashboard.render_analytics_page()
            else:
                st.error("User email not found. Please complete onboarding again.")

        elif current_page == "AI Assistant":
            render_ai_assistant_page()

        elif current_page == "Project Status":
            render_project_status_page()

        elif current_page == "Settings":
            render_settings_page()

    except Exception as e:
        st.error(f"An error occurred: {e}")
        logger.error(f"Page rendering error: {e}")


def render_settings_page():
    """Render the settings page."""
    st.header("⚙️ Settings")

    # Application settings
    st.subheader("🔧 Application Settings")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 🎯 Job Search Preferences")

        # Daily application limit
        daily_limit = st.slider(
            "Daily Application Limit",
            min_value=1, max_value=50,
            value=st.session_state.settings.daily_application_limit,
            help="Maximum applications to submit per day"
        )

        # Auto-apply setting
        auto_apply = st.checkbox(
            "Enable Auto-Apply",
            value=st.session_state.settings.auto_apply,
            help="Automatically apply to matching jobs"
        )

        # Manual review requirement
        manual_review = st.checkbox(
            "Require Manual Review",
            value=st.session_state.settings.require_manual_review,
            help="Review applications before submission"
        )

    with col2:
        st.markdown("#### 🔒 Privacy & Security")

        # Data retention
        retention_days = st.slider(
            "Data Retention (days)",
            min_value=30, max_value=365,
            value=st.session_state.settings.data_retention_days,
            help="How long to keep application data"
        )

        # Auto backup
        auto_backup = st.checkbox(
            "Enable Auto Backup",
            value=st.session_state.settings.auto_backup,
            help="Automatically backup your data"
        )

        # Encryption
        encrypt_data = st.checkbox(
            "Encrypt Sensitive Data",
            value=st.session_state.settings.encrypt_sensitive_data,
            help="Encrypt credentials and personal data"
        )

    # Save settings
    if st.button("💾 Save Settings", type="primary"):
        try:
            # Update settings
            st.session_state.settings.daily_application_limit = daily_limit
            st.session_state.settings.auto_apply = auto_apply
            st.session_state.settings.require_manual_review = manual_review
            st.session_state.settings.data_retention_days = retention_days
            st.session_state.settings.auto_backup = auto_backup
            st.session_state.settings.encrypt_sensitive_data = encrypt_data

            # Save to file
            save_user_data()
            st.success("✅ Settings saved successfully!")

        except Exception as e:
            st.error(f"Failed to save settings: {e}")

    # Export/Import section
    st.markdown("---")
    st.subheader("📤 Data Management")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📊 Export Applications", use_container_width=True):
            if st.session_state.user_email:
                try:
                    # Create temporary CSV file
                    import tempfile
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                        success = db_manager.export_applications_to_csv(st.session_state.user_email, f.name)
                        if success:
                            with open(f.name, 'r') as csv_file:
                                csv_data = csv_file.read()

                            st.download_button(
                                label="📥 Download CSV",
                                data=csv_data,
                                file_name=f"jobflow_applications_{datetime.now().strftime('%Y%m%d')}.csv",
                                mime="text/csv"
                            )
                        else:
                            st.error("Failed to export applications")
                except Exception as e:
                    st.error(f"Export failed: {e}")

    with col2:
        if st.button("🗑️ Clear All Data", use_container_width=True):
            st.warning("⚠️ This will delete all your application data!")
            if st.button("⚠️ Confirm Delete", type="secondary"):
                try:
                    # Clear onboarding files
                    data_dir = config_manager.get_data_dir()
                    onboarding_file = data_dir / "onboarding_complete.json"
                    user_file = data_dir / "current_user.json"

                    if onboarding_file.exists():
                        onboarding_file.unlink()
                    if user_file.exists():
                        user_file.unlink()

                    # Clear session state
                    for key in list(st.session_state.keys()):
                        del st.session_state[key]

                    st.success("✅ All data cleared! Refresh the page to restart onboarding.")

                except Exception as e:
                    st.error(f"Failed to clear data: {e}")

    with col3:
        if st.button("🔄 Reset Settings", use_container_width=True):
            st.session_state.settings = ApplicationSettings()
            save_user_data()
            st.success("Settings reset to defaults")
            st.rerun()


if __name__ == "__main__":
    main()
