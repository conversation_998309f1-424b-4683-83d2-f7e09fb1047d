"""
JobFlow - Modern Job Application Automation System
A beautiful, privacy-focused job application automation platform with SQL database,
state management, credential validation, and modern UI components.
"""

import streamlit as st
import streamlit_antd_components as sac
import streamlit_shadcn_ui as ui
from streamlit_option_menu import option_menu
import extra_streamlit_components as stx
import asyncio
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

# Import our modules
from models import (
    UserProfile, JobApplication, ApplicationSettings, SystemStatus,
    JobPortalName, ApplicationStatus, ExperienceLevel
)
from database.manager import db_manager
from components.onboarding import OnboardingFlow
from components.credentials import CredentialManager
from components.dashboard import Dashboard
from automation.api_client import api_manager
from utils.config import config_manager
from utils.logger import get_logger

# Configure page
st.set_page_config(
    page_title="JobFlow - Smart Job Application Automation",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced CSS for modern styling
st.markdown("""
<style>
    /* Main theme colors */
    :root {
        --primary-color: #667eea;
        --secondary-color: #764ba2;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
    }

    /* Header styling */
    .main-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }

    /* Card components */
    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border-left: 5px solid var(--primary-color);
        margin-bottom: 1.5rem;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    /* Metric cards */
    .metric-card {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    /* Status indicators */
    .status-success { color: var(--success-color); font-weight: bold; }
    .status-warning { color: var(--warning-color); font-weight: bold; }
    .status-danger { color: var(--danger-color); font-weight: bold; }
    .status-info { color: var(--info-color); font-weight: bold; }

    /* Button styling */
    .stButton > button {
        background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    }

    /* Sidebar styling */
    .css-1d391kg {
        background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    }

    /* Data tables */
    .dataframe {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    /* Progress bars */
    .stProgress > div > div > div > div {
        background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    }
</style>
""", unsafe_allow_html=True)

# Initialize logger
logger = get_logger(__name__)


def init_session_state():
    """Initialize session state variables with modern state management."""
    # Core application state
    if 'app_initialized' not in st.session_state:
        st.session_state.app_initialized = False

    # User authentication and profile
    if 'user_email' not in st.session_state:
        st.session_state.user_email = None
    if 'user_profile' not in st.session_state:
        st.session_state.user_profile = None

    # Onboarding state
    if 'onboarding_complete' not in st.session_state:
        st.session_state.onboarding_complete = False

    # Navigation state
    if 'current_page' not in st.session_state:
        st.session_state.current_page = "Dashboard"

    # Application data
    if 'applications' not in st.session_state:
        st.session_state.applications = []
    if 'search_history' not in st.session_state:
        st.session_state.search_history = []

    # Settings and configuration
    if 'settings' not in st.session_state:
        st.session_state.settings = ApplicationSettings()
    if 'system_status' not in st.session_state:
        st.session_state.system_status = SystemStatus()

    # UI state
    if 'show_notifications' not in st.session_state:
        st.session_state.show_notifications = True
    if 'theme_mode' not in st.session_state:
        st.session_state.theme_mode = "light"


def load_user_data():
    """Load user data from database and check onboarding status."""
    try:
        # Check if onboarding is complete
        onboarding_file = config_manager.get_data_dir() / "onboarding_complete.json"
        if onboarding_file.exists():
            st.session_state.onboarding_complete = True

            # Load the user email from onboarding data
            import json
            with open(onboarding_file, 'r') as f:
                onboarding_data = json.load(f)
                # Try to get user email from a separate file or database
                user_file = config_manager.get_data_dir() / "current_user.json"
                if user_file.exists():
                    with open(user_file, 'r') as uf:
                        user_data = json.load(uf)
                        st.session_state.user_email = user_data.get('email')

        # Load user profile from database if we have an email
        if st.session_state.user_email:
            profile = db_manager.get_user_profile(st.session_state.user_email)
            if profile:
                st.session_state.user_profile = profile

                # Load recent applications
                applications = db_manager.get_job_applications(st.session_state.user_email, limit=50)
                st.session_state.applications = applications

                logger.info(f"Loaded user data for {st.session_state.user_email}")
            else:
                logger.warning(f"No profile found for {st.session_state.user_email}")

    except Exception as e:
        logger.error(f"Error loading user data: {e}")
        # Reset state on error
        st.session_state.onboarding_complete = False
        st.session_state.user_email = None
        st.session_state.user_profile = None


def save_user_data():
    """Save user data to database with Pydantic v2 compatibility."""
    try:
        # Save user profile to database
        if st.session_state.user_profile and st.session_state.user_email:
            db_manager.save_user_profile(st.session_state.user_profile)

            # Save current user email for future sessions
            user_file = config_manager.get_data_dir() / "current_user.json"
            import json
            with open(user_file, 'w') as f:
                json.dump({"email": st.session_state.user_email}, f)

        # Mark onboarding as complete
        if st.session_state.onboarding_complete:
            onboarding_file = config_manager.get_data_dir() / "onboarding_complete.json"
            import json
            with open(onboarding_file, 'w') as f:
                json.dump({"completed": True, "date": datetime.now().isoformat()}, f)

        # Save settings to local file (using model_dump for Pydantic v2)
        if st.session_state.settings:
            settings_file = config_manager.get_data_dir() / "settings.json"
            import json
            with open(settings_file, 'w') as f:
                json.dump(st.session_state.settings.model_dump(), f, indent=2, default=str)

        logger.info("User data saved successfully")

    except Exception as e:
        logger.error(f"Error saving user data: {e}")
        st.error(f"Error saving user data: {e}")


def render_sidebar():
    """Render the modern sidebar navigation."""
    with st.sidebar:
        # User profile section
        if st.session_state.user_profile:
            st.markdown(f"""
            <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                <h4 style="color: white; margin: 0;">👤 {st.session_state.user_profile.name}</h4>
                <p style="color: rgba(255,255,255,0.8); margin: 0; font-size: 0.9em;">{st.session_state.user_email}</p>
            </div>
            """, unsafe_allow_html=True)

        # Navigation menu
        selected = option_menu(
            menu_title="JobFlow",
            options=["Dashboard", "Job Search", "Applications", "Analytics", "Credentials", "Settings"],
            icons=["speedometer2", "search", "briefcase", "graph-up", "key", "gear"],
            menu_icon="rocket-takeoff",
            default_index=0 if st.session_state.current_page == "Dashboard" else
                         (1 if st.session_state.current_page == "Job Search" else
                          2 if st.session_state.current_page == "Applications" else
                          3 if st.session_state.current_page == "Analytics" else
                          4 if st.session_state.current_page == "Credentials" else 5),
            styles={
                "container": {"padding": "0!important", "background-color": "transparent"},
                "icon": {"color": "white", "font-size": "18px"},
                "nav-link": {
                    "font-size": "16px",
                    "text-align": "left",
                    "margin": "0px",
                    "color": "white",
                    "background-color": "transparent",
                },
                "nav-link-selected": {"background-color": "rgba(255,255,255,0.2)"},
            }
        )

        # Update current page
        if selected != st.session_state.current_page:
            st.session_state.current_page = selected
            st.rerun()

        return selected


def render_job_search():
    """Render the modern job search page with enhanced UI."""
    st.header("🔍 Smart Job Search")

    if not st.session_state.user_profile:
        ui.alert(
            text="Please complete your profile setup first to enable job search.",
            type="warning",
            closable=True,
            key="profile_warning"
        )
        return

    # Search form with modern UI
    with st.form("job_search_form"):
        st.subheader("🎯 Search Parameters")

        col1, col2 = st.columns(2)
        with col1:
            keywords = st.text_input(
                "🔍 Keywords",
                value="python developer",
                placeholder="e.g., Python Developer, Data Scientist",
                help="Enter job keywords separated by commas"
            )
            location = st.text_input(
                "📍 Location",
                value="Remote",
                placeholder="Remote, New York, San Francisco"
            )

        with col2:
            portals = st.multiselect(
                "🌐 Job Portals",
                options=[portal.value for portal in JobPortalName],
                default=[JobPortalName.WE_WORK_REMOTELY.value],
                help="Select portals to search"
            )
            max_results = st.slider(
                "📊 Max Results per Portal",
                min_value=5, max_value=100, value=20, step=5
            )

        # Advanced filters
        with st.expander("🔧 Advanced Filters", expanded=False):
            col3, col4 = st.columns(2)
            with col3:
                salary_range = st.slider(
                    "💰 Salary Range (K)",
                    min_value=0, max_value=300, value=(50, 150), step=10
                )
                experience_filter = st.selectbox(
                    "📈 Experience Level",
                    options=["Any"] + [level.value for level in ExperienceLevel],
                    index=0
                )
            with col4:
                job_type_filter = st.selectbox(
                    "💼 Employment Type",
                    options=["Any", "Full-time", "Part-time", "Contract", "Freelance"],
                    index=0
                )
                remote_only = st.checkbox("🏠 Remote Only", value=True)

        # Search button
        search_clicked = st.form_submit_button(
            "🚀 Search Jobs",
            use_container_width=True,
            type="primary"
        )

    # Search results section
    if search_clicked:
        if not keywords.strip():
            st.error("Please enter search keywords.")
            return

        # Save search to history
        search_data = {
            'keywords': keywords,
            'location': location,
            'portals': portals,
            'timestamp': datetime.now()
        }
        if 'search_history' not in st.session_state:
            st.session_state.search_history = []
        st.session_state.search_history.insert(0, search_data)

        with st.spinner("🔍 Searching for jobs across portals..."):
            try:
                # Convert portal names back to enum
                selected_portals = [JobPortalName(portal) for portal in portals]
                keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]

                # Search jobs using API
                results = asyncio.run(api_manager.search_all_portals(
                    keywords=keyword_list,
                    location=location,
                    enabled_portals=selected_portals
                ))

                # Display results with modern UI
                total_jobs = sum(len(jobs) for jobs in results.values())

                if total_jobs > 0:
                    st.success(f"✅ Found {total_jobs} jobs across {len(results)} portals!")

                    # Results summary
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        ui.metric_card(
                            title="Total Jobs",
                            content=str(total_jobs),
                            key="total_jobs_metric"
                        )
                    with col2:
                        ui.metric_card(
                            title="Portals",
                            content=str(len([p for p in results.values() if p])),
                            key="portals_metric"
                        )
                    with col3:
                        avg_per_portal = total_jobs / len(results) if results else 0
                        ui.metric_card(
                            title="Avg/Portal",
                            content=f"{avg_per_portal:.1f}",
                            key="avg_portal_metric"
                        )
                    with col4:
                        ui.metric_card(
                            title="New Today",
                            content="0",  # TODO: Implement new jobs detection
                            key="new_jobs_metric"
                        )

                    # Display jobs by portal
                    for portal_name, jobs in results.items():
                        if jobs:
                            st.subheader(f"🌐 {portal_name.value} ({len(jobs)} jobs)")

                            for i, job in enumerate(jobs[:max_results]):
                                with st.expander(f"📋 {job['title']} - {job['company']}", expanded=False):
                                    col1, col2 = st.columns([3, 1])

                                    with col1:
                                        st.markdown(f"**🏢 Company:** {job['company']}")
                                        st.markdown(f"**📍 Location:** {job.get('location', 'Not specified')}")
                                        st.markdown(f"**📅 Posted:** {job.get('posted_date', 'Not specified')}")

                                        if job.get('description'):
                                            st.markdown("**📝 Description:**")
                                            description = job['description'][:400] + "..." if len(job['description']) > 400 else job['description']
                                            st.markdown(description)

                                    with col2:
                                        # CV Optimization button
                                        if st.button("✨ Optimize CV", key=f"optimize_{portal_name.value}_{i}"):
                                            st.session_state[f"show_optimization_{portal_name.value}_{i}"] = True
                                            st.rerun()

                                        if st.button("💼 Apply", key=f"apply_{portal_name.value}_{i}"):
                                            # TODO: Implement application logic
                                            st.info("🚧 Application feature coming soon!")

                                        if st.button("🔗 View Job", key=f"view_{portal_name.value}_{i}"):
                                            st.markdown(f"[🔗 Open Job Posting]({job['url']})")

                                        if st.button("💾 Save", key=f"save_{portal_name.value}_{i}"):
                                            # TODO: Implement save job logic
                                            st.success("Job saved!")

                                # CV Optimization modal
                                if st.session_state.get(f"show_optimization_{portal_name.value}_{i}", False):
                                    st.markdown("---")
                                    st.subheader("✨ CV Optimization")

                                    # Check if user has AI credentials
                                    from utils.cv_optimizer import cv_optimizer
                                    available_providers = cv_optimizer.get_available_providers(st.session_state.user_email)

                                    if not available_providers:
                                        st.warning("⚠️ No AI credentials found. Add OpenAI or Anthropic API keys in the Credentials section to enable CV optimization.")
                                        if st.button("❌ Close", key=f"close_opt_{portal_name.value}_{i}"):
                                            st.session_state[f"show_optimization_{portal_name.value}_{i}"] = False
                                            st.rerun()
                                    else:
                                        # Show optimization options
                                        provider = st.selectbox(
                                            "AI Provider",
                                            options=available_providers,
                                            format_func=lambda x: "OpenAI (GPT-4)" if x == "openai" else "Anthropic (Claude)",
                                            key=f"provider_{portal_name.value}_{i}"
                                        )

                                        col_opt1, col_opt2 = st.columns(2)

                                        with col_opt1:
                                            if st.button("🔍 Analyze Match", key=f"analyze_{portal_name.value}_{i}"):
                                                with st.spinner("Analyzing job match..."):
                                                    # Get CV text from user profile
                                                    cv_text = getattr(st.session_state.user_profile, 'cv_text', '')
                                                    if not cv_text:
                                                        st.error("No CV text found. Please re-upload your CV.")
                                                    else:
                                                        analysis = cv_optimizer.analyze_job_match(
                                                            cv_text, job.get('description', ''),
                                                            st.session_state.user_email, provider
                                                        )

                                                        if analysis:
                                                            st.success(f"**Match Score: {analysis.get('match_score', 0)}%**")

                                                            col_a1, col_a2 = st.columns(2)
                                                            with col_a1:
                                                                st.write("**Matching Skills:**")
                                                                for skill in analysis.get('matching_skills', []):
                                                                    st.write(f"✅ {skill}")

                                                            with col_a2:
                                                                st.write("**Missing Skills:**")
                                                                for skill in analysis.get('missing_skills', []):
                                                                    st.write(f"❌ {skill}")

                                                            st.write("**Recommendations:**")
                                                            for rec in analysis.get('recommendations', []):
                                                                st.write(f"💡 {rec}")

                                        with col_opt2:
                                            if st.button("✨ Optimize CV", key=f"opt_cv_{portal_name.value}_{i}"):
                                                with st.spinner("Optimizing CV for this job..."):
                                                    # Get CV text from user profile
                                                    cv_text = getattr(st.session_state.user_profile, 'cv_text', '')
                                                    if not cv_text:
                                                        st.error("No CV text found. Please re-upload your CV.")
                                                    else:
                                                        optimized_cv = cv_optimizer.optimize_cv_for_job(
                                                            cv_text, job.get('description', ''),
                                                            st.session_state.user_email, provider
                                                        )

                                                        if optimized_cv:
                                                            st.success("✅ CV optimized successfully!")

                                                            # Show download button
                                                            st.download_button(
                                                                label="📥 Download Optimized CV",
                                                                data=optimized_cv,
                                                                file_name=f"optimized_cv_{job['company'].replace(' ', '_')}.txt",
                                                                mime="text/plain"
                                                            )

                                                            # Show preview
                                                            with st.expander("👀 Preview Optimized CV"):
                                                                st.text_area(
                                                                    "Optimized CV Content",
                                                                    value=optimized_cv[:1000] + "..." if len(optimized_cv) > 1000 else optimized_cv,
                                                                    height=200,
                                                                    disabled=True
                                                                )

                                        # Close button
                                        if st.button("❌ Close", key=f"close_opt_{portal_name.value}_{i}"):
                                            st.session_state[f"show_optimization_{portal_name.value}_{i}"] = False
                                            st.rerun()
                else:
                    st.warning("😔 No jobs found matching your criteria. Try different keywords or expand your search.")

            except Exception as e:
                st.error(f"❌ Error searching jobs: {e}")
                logger.error(f"Job search error: {e}")


def render_applications_page():
    """Render the applications tracking page."""
    st.header("📋 Application Tracker")

    if not st.session_state.user_email:
        st.warning("Please complete onboarding to view applications.")
        return

    # Get applications from database
    applications = db_manager.get_job_applications(st.session_state.user_email, limit=100)

    if not applications:
        st.info("📝 No applications yet. Start by searching for jobs!")
        if st.button("🔍 Search Jobs", type="primary"):
            st.session_state.current_page = "Job Search"
            st.rerun()
        return

    # Applications summary
    col1, col2, col3, col4 = st.columns(4)

    status_counts = {}
    for app in applications:
        status_counts[app.status.value] = status_counts.get(app.status.value, 0) + 1

    with col1:
        ui.metric_card(
            title="Total Applications",
            content=str(len(applications)),
            key="total_apps"
        )
    with col2:
        interviews = status_counts.get('interview', 0)
        ui.metric_card(
            title="Interviews",
            content=str(interviews),
            key="interviews"
        )
    with col3:
        offers = status_counts.get('offer', 0)
        ui.metric_card(
            title="Offers",
            content=str(offers),
            key="offers"
        )
    with col4:
        success_rate = (offers / len(applications) * 100) if applications else 0
        ui.metric_card(
            title="Success Rate",
            content=f"{success_rate:.1f}%",
            key="success_rate"
        )

    # Applications table
    st.subheader("📊 Recent Applications")

    # Create DataFrame for display
    data = []
    for app in applications:
        data.append({
            'Position': app.position_title,
            'Company': app.company_name,
            'Portal': app.portal_name.value,
            'Status': app.status.value,
            'Applied': app.submission_timestamp.strftime('%Y-%m-%d') if app.submission_timestamp else 'Not submitted',
            'Created': app.created_at.strftime('%Y-%m-%d')
        })

    df = pd.DataFrame(data)

    # Display with filters
    col1, col2, col3 = st.columns(3)
    with col1:
        status_filter = st.selectbox(
            "Filter by Status",
            options=["All"] + list(set(df['Status'])),
            index=0
        )
    with col2:
        portal_filter = st.selectbox(
            "Filter by Portal",
            options=["All"] + list(set(df['Portal'])),
            index=0
        )
    with col3:
        sort_by = st.selectbox(
            "Sort by",
            options=["Created", "Applied", "Company", "Position"],
            index=0
        )

    # Apply filters
    filtered_df = df.copy()
    if status_filter != "All":
        filtered_df = filtered_df[filtered_df['Status'] == status_filter]
    if portal_filter != "All":
        filtered_df = filtered_df[filtered_df['Portal'] == portal_filter]

    # Sort
    filtered_df = filtered_df.sort_values(by=sort_by, ascending=False)

    # Display table
    st.dataframe(
        filtered_df,
        use_container_width=True,
        hide_index=True,
        column_config={
            'Position': st.column_config.TextColumn('Position', width='large'),
            'Company': st.column_config.TextColumn('Company', width='medium'),
            'Portal': st.column_config.TextColumn('Portal', width='small'),
            'Status': st.column_config.TextColumn('Status', width='small'),
            'Applied': st.column_config.TextColumn('Applied', width='small'),
            'Created': st.column_config.TextColumn('Created', width='small')
        }
    )
def main():
    """Main application function with modern architecture."""
    # Initialize session state
    init_session_state()

    # Initialize database on first run
    if not st.session_state.app_initialized:
        try:
            # Initialize database
            db_manager.init_database()
            st.session_state.app_initialized = True
            logger.info("Application initialized successfully")
        except Exception as e:
            st.error(f"Failed to initialize application: {e}")
            logger.error(f"App initialization failed: {e}")
            return

    # Load user data on startup
    if 'data_loaded' not in st.session_state:
        load_user_data()
        st.session_state.data_loaded = True

    # Check if onboarding is complete
    if not st.session_state.onboarding_complete:
        # Render onboarding flow
        onboarding = OnboardingFlow()
        completed = onboarding.render()

        if completed:
            # Onboarding completed, reload the app
            st.rerun()
        return

    # Render sidebar navigation
    current_page = render_sidebar()

    # Render main content based on selected page
    try:
        if current_page == "Dashboard":
            if st.session_state.user_email:
                dashboard = Dashboard(st.session_state.user_email)
                dashboard.render()
            else:
                st.error("User email not found. Please complete onboarding again.")

        elif current_page == "Job Search":
            render_job_search()

        elif current_page == "Applications":
            render_applications_page()

        elif current_page == "Analytics":
            if st.session_state.user_email:
                dashboard = Dashboard(st.session_state.user_email)
                dashboard.render_analytics_page()
            else:
                st.error("User email not found. Please complete onboarding again.")

        elif current_page == "Credentials":
            st.header("🔐 Credential Management")
            credential_manager = CredentialManager()
            credential_manager.render_credentials_overview()

        elif current_page == "Settings":
            render_settings_page()

    except Exception as e:
        st.error(f"An error occurred: {e}")
        logger.error(f"Page rendering error: {e}")


def render_settings_page():
    """Render the settings page."""
    st.header("⚙️ Settings")

    # Application settings
    st.subheader("🔧 Application Settings")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 🎯 Job Search Preferences")

        # Daily application limit
        daily_limit = st.slider(
            "Daily Application Limit",
            min_value=1, max_value=50,
            value=st.session_state.settings.daily_application_limit,
            help="Maximum applications to submit per day"
        )

        # Auto-apply setting
        auto_apply = st.checkbox(
            "Enable Auto-Apply",
            value=st.session_state.settings.auto_apply,
            help="Automatically apply to matching jobs"
        )

        # Manual review requirement
        manual_review = st.checkbox(
            "Require Manual Review",
            value=st.session_state.settings.require_manual_review,
            help="Review applications before submission"
        )

    with col2:
        st.markdown("#### 🔒 Privacy & Security")

        # Data retention
        retention_days = st.slider(
            "Data Retention (days)",
            min_value=30, max_value=365,
            value=st.session_state.settings.data_retention_days,
            help="How long to keep application data"
        )

        # Auto backup
        auto_backup = st.checkbox(
            "Enable Auto Backup",
            value=st.session_state.settings.auto_backup,
            help="Automatically backup your data"
        )

        # Encryption
        encrypt_data = st.checkbox(
            "Encrypt Sensitive Data",
            value=st.session_state.settings.encrypt_sensitive_data,
            help="Encrypt credentials and personal data"
        )

    # Save settings
    if st.button("💾 Save Settings", type="primary"):
        try:
            # Update settings
            st.session_state.settings.daily_application_limit = daily_limit
            st.session_state.settings.auto_apply = auto_apply
            st.session_state.settings.require_manual_review = manual_review
            st.session_state.settings.data_retention_days = retention_days
            st.session_state.settings.auto_backup = auto_backup
            st.session_state.settings.encrypt_sensitive_data = encrypt_data

            # Save to file
            save_user_data()
            st.success("✅ Settings saved successfully!")

        except Exception as e:
            st.error(f"Failed to save settings: {e}")

    # Export/Import section
    st.markdown("---")
    st.subheader("📤 Data Management")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📊 Export Applications", use_container_width=True):
            if st.session_state.user_email:
                try:
                    # Create temporary CSV file
                    import tempfile
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                        success = db_manager.export_applications_to_csv(st.session_state.user_email, f.name)
                        if success:
                            with open(f.name, 'r') as csv_file:
                                csv_data = csv_file.read()

                            st.download_button(
                                label="📥 Download CSV",
                                data=csv_data,
                                file_name=f"jobflow_applications_{datetime.now().strftime('%Y%m%d')}.csv",
                                mime="text/csv"
                            )
                        else:
                            st.error("Failed to export applications")
                except Exception as e:
                    st.error(f"Export failed: {e}")

    with col2:
        if st.button("🗑️ Clear All Data", use_container_width=True):
            st.warning("⚠️ This will delete all your application data!")
            if st.button("⚠️ Confirm Delete", type="secondary"):
                try:
                    # Clear onboarding files
                    data_dir = config_manager.get_data_dir()
                    onboarding_file = data_dir / "onboarding_complete.json"
                    user_file = data_dir / "current_user.json"

                    if onboarding_file.exists():
                        onboarding_file.unlink()
                    if user_file.exists():
                        user_file.unlink()

                    # Clear session state
                    for key in list(st.session_state.keys()):
                        del st.session_state[key]

                    st.success("✅ All data cleared! Refresh the page to restart onboarding.")

                except Exception as e:
                    st.error(f"Failed to clear data: {e}")

    with col3:
        if st.button("🔄 Reset Settings", use_container_width=True):
            st.session_state.settings = ApplicationSettings()
            save_user_data()
            st.success("Settings reset to defaults")
            st.rerun()


if __name__ == "__main__":
    main()
