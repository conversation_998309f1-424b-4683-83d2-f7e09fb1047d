"""
CV Optimizer using LLM to intelligently tweak CVs for specific job descriptions.
Makes small, targeted optimizations while maintaining the original structure and style.
"""

import openai
import anthropic
from typing import Dict, Any, Optional, List
import streamlit as st
from datetime import datetime

from database.manager import db_manager
from utils.logger import get_logger

logger = get_logger(__name__)


class CVOptimizer:
    """Intelligent CV optimizer that uses LLM to tailor CVs for specific jobs."""
    
    def __init__(self):
        self.system_prompt = """You are an expert CV optimization assistant. Your job is to make small, targeted tweaks to a CV to better match a specific job description while maintaining the original structure, style, and truthfulness.

IMPORTANT GUIDELINES:
1. NEVER add false information or skills the person doesn't have
2. Make only SMALL, SUBTLE changes to improve keyword matching
3. Maintain the original formatting and structure
4. Focus on rephrasing existing content to better align with job requirements
5. Highlight relevant skills and experience that match the job
6. Use industry-standard terminology from the job description
7. Keep the same overall length and sections

OPTIMIZATION STRATEGIES:
- Rephrase bullet points to include relevant keywords from job description
- Adjust skill descriptions to match job requirements terminology
- Emphasize relevant experience and de-emphasize less relevant parts
- Use action verbs and metrics that align with the job posting
- Ensure technical skills mentioned in job description are prominently featured (if person has them)

OUTPUT FORMAT:
Return the optimized CV text maintaining the exact same structure as the original."""
    
    def optimize_cv_for_job(self, cv_text: str, job_description: str, 
                           user_email: str, provider: str = "openai") -> Optional[str]:
        """Optimize CV for a specific job description using LLM."""
        try:
            # Get API credentials
            credentials = db_manager.get_credential(user_email, provider)
            if not credentials:
                st.warning(f"No {provider} credentials found. Please add them in the Credentials section.")
                return None
            
            # Create optimization prompt
            user_prompt = f"""
ORIGINAL CV:
{cv_text}

JOB DESCRIPTION:
{job_description}

Please optimize this CV for the job description above. Make small, targeted improvements to better match the job requirements while keeping all information truthful and maintaining the original structure.
"""
            
            # Call LLM based on provider
            if provider == "openai":
                optimized_cv = self._optimize_with_openai(credentials, user_prompt)
            elif provider == "anthropic":
                optimized_cv = self._optimize_with_anthropic(credentials, user_prompt)
            else:
                raise ValueError(f"Unsupported provider: {provider}")
            
            if optimized_cv:
                logger.info(f"Successfully optimized CV using {provider}")
                return optimized_cv
            else:
                logger.warning(f"CV optimization returned empty result from {provider}")
                return None
                
        except Exception as e:
            logger.error(f"CV optimization failed: {e}")
            st.error(f"CV optimization failed: {e}")
            return None
    
    def _optimize_with_openai(self, credentials: Dict[str, Any], user_prompt: str) -> Optional[str]:
        """Optimize CV using OpenAI API."""
        try:
            client = openai.OpenAI(api_key=credentials['api_key'])
            
            response = client.chat.completions.create(
                model="gpt-4o-mini",  # Use the more cost-effective model
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=4000,
                temperature=0.3  # Lower temperature for more consistent results
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise
    
    def _optimize_with_anthropic(self, credentials: Dict[str, Any], user_prompt: str) -> Optional[str]:
        """Optimize CV using Anthropic Claude API."""
        try:
            client = anthropic.Anthropic(api_key=credentials['api_key'])
            
            response = client.messages.create(
                model="claude-3-haiku-20240307",  # Use the more cost-effective model
                max_tokens=4000,
                temperature=0.3,
                system=self.system_prompt,
                messages=[
                    {"role": "user", "content": user_prompt}
                ]
            )
            
            return response.content[0].text.strip()
            
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            raise
    
    def analyze_job_match(self, cv_text: str, job_description: str, 
                         user_email: str, provider: str = "openai") -> Optional[Dict[str, Any]]:
        """Analyze how well a CV matches a job description."""
        try:
            credentials = db_manager.get_credential(user_email, provider)
            if not credentials:
                return None
            
            analysis_prompt = f"""
Analyze how well this CV matches the job description. Provide a detailed analysis.

CV:
{cv_text}

JOB DESCRIPTION:
{job_description}

Please provide:
1. Match Score (0-100)
2. Matching Skills
3. Missing Skills
4. Recommendations for improvement
5. Key strengths for this role

Format as JSON:
{{
    "match_score": 85,
    "matching_skills": ["Python", "React", "AWS"],
    "missing_skills": ["Kubernetes", "GraphQL"],
    "recommendations": ["Emphasize cloud experience", "Highlight API development"],
    "key_strengths": ["Strong backend experience", "Leadership skills"]
}}
"""
            
            if provider == "openai":
                result = self._analyze_with_openai(credentials, analysis_prompt)
            else:
                result = self._analyze_with_anthropic(credentials, analysis_prompt)
            
            # Parse JSON response
            import json
            return json.loads(result) if result else None
            
        except Exception as e:
            logger.error(f"Job match analysis failed: {e}")
            return None
    
    def _analyze_with_openai(self, credentials: Dict[str, Any], prompt: str) -> Optional[str]:
        """Analyze job match using OpenAI."""
        try:
            client = openai.OpenAI(api_key=credentials['api_key'])
            
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a job matching expert. Analyze CVs and job descriptions to provide detailed matching insights."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.1
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI analysis error: {e}")
            raise
    
    def _analyze_with_anthropic(self, credentials: Dict[str, Any], prompt: str) -> Optional[str]:
        """Analyze job match using Anthropic."""
        try:
            client = anthropic.Anthropic(api_key=credentials['api_key'])
            
            response = client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=1000,
                temperature=0.1,
                system="You are a job matching expert. Analyze CVs and job descriptions to provide detailed matching insights.",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return response.content[0].text.strip()
            
        except Exception as e:
            logger.error(f"Anthropic analysis error: {e}")
            raise
    
    def generate_cover_letter(self, cv_text: str, job_description: str, 
                            company_name: str, position_title: str,
                            user_email: str, provider: str = "openai") -> Optional[str]:
        """Generate a personalized cover letter for a specific job."""
        try:
            credentials = db_manager.get_credential(user_email, provider)
            if not credentials:
                return None
            
            cover_letter_prompt = f"""
Generate a professional, personalized cover letter for this job application.

CV SUMMARY:
{cv_text[:2000]}  # Truncate for token efficiency

JOB DESCRIPTION:
{job_description}

COMPANY: {company_name}
POSITION: {position_title}

Requirements:
1. Professional tone, 3-4 paragraphs
2. Highlight relevant experience from CV
3. Show enthusiasm for the specific role and company
4. Include specific examples of achievements
5. End with a strong call to action
6. Keep it concise (under 400 words)

Format as a complete cover letter ready to send.
"""
            
            if provider == "openai":
                result = self._generate_cover_letter_openai(credentials, cover_letter_prompt)
            else:
                result = self._generate_cover_letter_anthropic(credentials, cover_letter_prompt)
            
            return result
            
        except Exception as e:
            logger.error(f"Cover letter generation failed: {e}")
            return None
    
    def _generate_cover_letter_openai(self, credentials: Dict[str, Any], prompt: str) -> Optional[str]:
        """Generate cover letter using OpenAI."""
        try:
            client = openai.OpenAI(api_key=credentials['api_key'])
            
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are an expert cover letter writer. Create compelling, personalized cover letters that highlight relevant experience and show genuine interest in the role."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI cover letter error: {e}")
            raise
    
    def _generate_cover_letter_anthropic(self, credentials: Dict[str, Any], prompt: str) -> Optional[str]:
        """Generate cover letter using Anthropic."""
        try:
            client = anthropic.Anthropic(api_key=credentials['api_key'])
            
            response = client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=800,
                temperature=0.7,
                system="You are an expert cover letter writer. Create compelling, personalized cover letters that highlight relevant experience and show genuine interest in the role.",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            return response.content[0].text.strip()
            
        except Exception as e:
            logger.error(f"Anthropic cover letter error: {e}")
            raise
    
    def get_available_providers(self, user_email: str) -> List[str]:
        """Get list of available LLM providers based on stored credentials."""
        providers = []
        
        if db_manager.get_credential(user_email, "openai"):
            providers.append("openai")
        
        if db_manager.get_credential(user_email, "anthropic"):
            providers.append("anthropic")
        
        return providers


# Global CV optimizer instance
cv_optimizer = CVOptimizer()
