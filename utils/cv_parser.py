"""
CV Parser utility for extracting profile information from uploaded CVs.
Supports PDF and DOCX formats with intelligent text extraction and parsing.
"""

import re
import PyPDF2
import docx
from typing import Dict, List, Optional, Any
from pathlib import Path
import streamlit as st
from datetime import datetime

from models import UserProfile, ExperienceLevel
from utils.logger import get_logger

logger = get_logger(__name__)


class CVParser:
    """Intelligent CV parser that extracts profile information from uploaded documents."""
    
    def __init__(self):
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_pattern = re.compile(r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}')
        
        # Common skill keywords
        self.tech_skills = {
            'python', 'javascript', 'java', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift',
            'react', 'angular', 'vue', 'node.js', 'express', 'django', 'flask', 'spring',
            'sql', 'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch',
            'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'git', 'github',
            'machine learning', 'ai', 'data science', 'tensorflow', 'pytorch', 'pandas',
            'html', 'css', 'sass', 'bootstrap', 'tailwind', 'figma', 'photoshop'
        }
        
        # Experience level indicators
        self.experience_indicators = {
            'entry': ['intern', 'junior', 'entry', 'graduate', 'trainee', 'associate'],
            'mid': ['developer', 'engineer', 'analyst', 'specialist', 'consultant'],
            'senior': ['senior', 'lead', 'principal', 'staff', 'architect'],
            'executive': ['director', 'manager', 'head', 'vp', 'cto', 'ceo', 'founder']
        }
    
    def parse_cv(self, file_path: str) -> Dict[str, Any]:
        """Parse CV and extract profile information."""
        try:
            # Extract text based on file type
            if file_path.lower().endswith('.pdf'):
                text = self._extract_pdf_text(file_path)
            elif file_path.lower().endswith(('.docx', '.doc')):
                text = self._extract_docx_text(file_path)
            else:
                raise ValueError("Unsupported file format. Please upload PDF or DOCX files.")
            
            if not text.strip():
                raise ValueError("Could not extract text from the CV. Please check the file.")
            
            # Parse extracted text
            profile_data = self._parse_text(text)
            
            logger.info(f"Successfully parsed CV: {file_path}")
            return profile_data
            
        except Exception as e:
            logger.error(f"Failed to parse CV {file_path}: {e}")
            raise
    
    def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from PDF file."""
        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            logger.error(f"Failed to extract PDF text: {e}")
            raise ValueError("Could not read PDF file. Please ensure it's not password protected.")
        
        return text
    
    def _extract_docx_text(self, file_path: str) -> str:
        """Extract text from DOCX file."""
        text = ""
        try:
            doc = docx.Document(file_path)
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
        except Exception as e:
            logger.error(f"Failed to extract DOCX text: {e}")
            raise ValueError("Could not read DOCX file. Please ensure it's a valid Word document.")
        
        return text
    
    def _parse_text(self, text: str) -> Dict[str, Any]:
        """Parse extracted text to extract profile information."""
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        # Extract basic information
        name = self._extract_name(lines)
        email = self._extract_email(text)
        phone = self._extract_phone(text)
        
        # Extract skills
        skills = self._extract_skills(text)
        
        # Determine experience level
        experience_level = self._determine_experience_level(text)
        
        # Extract years of experience
        years_experience = self._extract_years_experience(text)
        
        # Extract job preferences (basic extraction)
        job_types = self._extract_job_types(text)
        
        return {
            'name': name,
            'email': email,
            'phone': phone,
            'skills': skills,
            'experience_level': experience_level,
            'years_of_experience': years_experience,
            'preferred_job_types': job_types,
            'preferred_locations': ['Remote'],  # Default to remote
            'cv_text': text  # Store original text for LLM optimization
        }
    
    def _extract_name(self, lines: List[str]) -> str:
        """Extract name from CV (usually the first line or header)."""
        # Look for name in first few lines
        for line in lines[:5]:
            # Skip lines that look like contact info or headers
            if any(keyword in line.lower() for keyword in ['email', 'phone', 'address', 'linkedin', 'github']):
                continue
            
            # Check if line looks like a name (2-4 words, proper case)
            words = line.split()
            if 2 <= len(words) <= 4 and all(word.istitle() or word.isupper() for word in words):
                return line
        
        # Fallback: return first non-empty line
        return lines[0] if lines else "Unknown"
    
    def _extract_email(self, text: str) -> Optional[str]:
        """Extract email address from CV text."""
        matches = self.email_pattern.findall(text)
        return matches[0] if matches else None
    
    def _extract_phone(self, text: str) -> Optional[str]:
        """Extract phone number from CV text."""
        matches = self.phone_pattern.findall(text)
        return matches[0] if matches else None
    
    def _extract_skills(self, text: str) -> List[str]:
        """Extract technical skills from CV text."""
        text_lower = text.lower()
        found_skills = []
        
        for skill in self.tech_skills:
            if skill in text_lower:
                # Add the skill with proper capitalization
                found_skills.append(skill.title())
        
        # Remove duplicates and sort
        return sorted(list(set(found_skills)))
    
    def _determine_experience_level(self, text: str) -> str:
        """Determine experience level based on CV content."""
        text_lower = text.lower()
        
        # Count indicators for each level
        level_scores = {level: 0 for level in self.experience_indicators.keys()}
        
        for level, indicators in self.experience_indicators.items():
            for indicator in indicators:
                level_scores[level] += text_lower.count(indicator)
        
        # Return level with highest score, default to 'mid'
        max_level = max(level_scores, key=level_scores.get)
        return max_level if level_scores[max_level] > 0 else 'mid'
    
    def _extract_years_experience(self, text: str) -> int:
        """Extract years of experience from CV text."""
        # Look for patterns like "5 years", "3+ years", etc.
        patterns = [
            r'(\d+)\+?\s*years?\s*(?:of\s*)?experience',
            r'(\d+)\+?\s*years?\s*in',
            r'experience.*?(\d+)\+?\s*years?'
        ]
        
        years = []
        for pattern in patterns:
            matches = re.findall(pattern, text.lower())
            years.extend([int(match) for match in matches])
        
        # Return the maximum found, or estimate based on experience level
        if years:
            return max(years)
        
        # Fallback estimation based on experience level
        level_estimates = {
            'entry': 1,
            'junior': 2,
            'mid': 5,
            'senior': 8,
            'lead': 10,
            'executive': 15
        }
        
        experience_level = self._determine_experience_level(text)
        return level_estimates.get(experience_level, 3)
    
    def _extract_job_types(self, text: str) -> List[str]:
        """Extract preferred job types from CV content."""
        text_lower = text.lower()
        job_types = []
        
        # Common job title patterns
        job_patterns = {
            'Software Engineer': ['software engineer', 'software developer', 'developer'],
            'Data Scientist': ['data scientist', 'data analyst', 'machine learning'],
            'Frontend Developer': ['frontend', 'front-end', 'ui developer'],
            'Backend Developer': ['backend', 'back-end', 'api developer'],
            'Full Stack Developer': ['full stack', 'fullstack'],
            'DevOps Engineer': ['devops', 'site reliability', 'infrastructure'],
            'Product Manager': ['product manager', 'product owner'],
            'Designer': ['designer', 'ux', 'ui design']
        }
        
        for job_type, keywords in job_patterns.items():
            if any(keyword in text_lower for keyword in keywords):
                job_types.append(job_type)
        
        return job_types if job_types else ['Software Developer']  # Default
    
    def create_user_profile(self, cv_data: Dict[str, Any]) -> UserProfile:
        """Create a UserProfile object from parsed CV data."""
        try:
            # Map experience level to enum
            experience_level_map = {
                'entry': ExperienceLevel.ENTRY,
                'junior': ExperienceLevel.JUNIOR,
                'mid': ExperienceLevel.MID,
                'senior': ExperienceLevel.SENIOR,
                'lead': ExperienceLevel.LEAD,
                'executive': ExperienceLevel.EXECUTIVE
            }
            
            experience_level = experience_level_map.get(
                cv_data.get('experience_level', 'mid'), 
                ExperienceLevel.MID
            )
            
            profile_data = {
                'name': cv_data.get('name', 'Unknown'),
                'email': cv_data.get('email', ''),
                'phone': cv_data.get('phone'),
                'skills': cv_data.get('skills', []),
                'experience_level': experience_level,
                'years_of_experience': cv_data.get('years_of_experience', 0),
                'preferred_job_types': cv_data.get('preferred_job_types', []),
                'preferred_locations': cv_data.get('preferred_locations', ['Remote']),
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }
            
            return UserProfile(**profile_data)
            
        except Exception as e:
            logger.error(f"Failed to create user profile from CV data: {e}")
            raise


# Global CV parser instance
cv_parser = CVParser()
