"""
Credential management components for job application automation system.
Provides secure credential storage and validation with modern UI.
"""

import streamlit as st
import streamlit_antd_components as sac
import streamlit_shadcn_ui as ui
import httpx
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

from database.manager import db_manager
from utils.logger import get_logger

logger = get_logger(__name__)


class CredentialManager:
    """Manages API credentials with validation and secure storage."""
    
    def __init__(self):
        self.supported_services = {
            'openai': {
                'name': 'OpenAI API',
                'type': 'api_key',
                'fields': ['api_key'],
                'description': 'For AI-powered cover letter generation and job matching',
                'validation_url': 'https://api.openai.com/v1/models',
                'icon': '🤖'
            },
            'linkedin': {
                'name': 'LinkedIn',
                'type': 'oauth',
                'fields': ['client_id', 'client_secret'],
                'description': 'For LinkedIn job search and profile integration',
                'validation_url': None,  # OAuth validation is more complex
                'icon': '💼'
            },
            'indeed': {
                'name': 'Indeed API',
                'type': 'api_key',
                'fields': ['publisher_id'],
                'description': 'For Indeed job search integration',
                'validation_url': None,  # Indeed API validation
                'icon': '🔍'
            },
            'github': {
                'name': 'GitHub',
                'type': 'token',
                'fields': ['access_token'],
                'description': 'For portfolio integration and developer job matching',
                'validation_url': 'https://api.github.com/user',
                'icon': '🐙'
            },
            'anthropic': {
                'name': 'Anthropic Claude',
                'type': 'api_key',
                'fields': ['api_key'],
                'description': 'Alternative AI provider for content generation',
                'validation_url': 'https://api.anthropic.com/v1/messages',
                'icon': '🧠'
            }
        }
    
    def render_setup_form(self):
        """Render the credential setup form."""
        st.markdown("""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                    color: white; padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
            <h4>🔐 Secure Credential Storage</h4>
            <p>All credentials are encrypted and stored locally on your device. 
               We never transmit your API keys to external servers.</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Service selection using simple selectbox for now
        service_names = list(self.supported_services.keys())
        service_options = {
            f"{self.supported_services[service]['icon']} {self.supported_services[service]['name']}": service
            for service in service_names
        }

        selected_service_label = st.selectbox(
            "Select Service to Configure",
            options=list(service_options.keys()),
            index=0,
            key="credential_service_selector"
        )

        if selected_service_label:
            service_key = service_options[selected_service_label]
            self._render_service_form(service_key)
    
    def _render_service_form(self, service_key: str):
        """Render form for specific service."""
        service = self.supported_services[service_key]
        
        st.markdown(f"""
        ### {service['icon']} {service['name']}
        {service['description']}
        """)
        
        # Check if credentials already exist
        user_email = st.session_state.onboarding_data.get('email', '')
        existing_creds = None
        if user_email:
            existing_creds = db_manager.get_credential(user_email, service_key)
        
        # Credential form
        with st.form(f"credential_form_{service_key}"):
            credential_data = {}
            
            for field in service['fields']:
                field_label = field.replace('_', ' ').title()
                field_type = "password" if 'key' in field or 'secret' in field or 'token' in field else "default"
                
                if field_type == "password":
                    value = st.text_input(
                        f"{field_label} *",
                        type="password",
                        value="••••••••" if existing_creds and field in existing_creds else "",
                        help=f"Enter your {service['name']} {field_label.lower()}"
                    )
                else:
                    value = st.text_input(
                        f"{field_label} *",
                        value=existing_creds.get(field, '') if existing_creds else "",
                        help=f"Enter your {service['name']} {field_label.lower()}"
                    )
                
                if value and value != "••••••••":
                    credential_data[field] = value
            
            # Form buttons
            col1, col2, col3 = st.columns([1, 1, 1])
            
            with col1:
                save_clicked = st.form_submit_button(
                    "💾 Save Credentials",
                    use_container_width=True,
                    type="primary"
                )
            
            with col2:
                validate_clicked = st.form_submit_button(
                    "✅ Validate",
                    use_container_width=True
                )
            
            with col3:
                if existing_creds:
                    delete_clicked = st.form_submit_button(
                        "🗑️ Delete",
                        use_container_width=True
                    )
                else:
                    delete_clicked = False
            
            # Handle form submissions
            if save_clicked:
                self._handle_save_credentials(service_key, service, credential_data)
            
            if validate_clicked:
                self._handle_validate_credentials(service_key, service, credential_data)
            
            if delete_clicked:
                self._handle_delete_credentials(service_key, service)
        
        # Show validation status
        self._show_validation_status(service_key)
    
    def _handle_save_credentials(self, service_key: str, service: Dict[str, Any], 
                                credential_data: Dict[str, str]):
        """Handle saving credentials."""
        user_email = st.session_state.onboarding_data.get('email', '')
        if not user_email:
            st.error("User email not found. Please complete profile setup first.")
            return
        
        # Validate required fields
        missing_fields = [field for field in service['fields'] if field not in credential_data]
        if missing_fields:
            st.error(f"Please fill in all required fields: {', '.join(missing_fields)}")
            return
        
        # Save credentials
        success = db_manager.save_credential(
            user_email=user_email,
            service_name=service_key,
            credential_type=service['type'],
            credential_data=credential_data
        )
        
        if success:
            st.success(f"✅ {service['name']} credentials saved successfully!")
            logger.info(f"Saved credentials for {service_key}")
        else:
            st.error(f"❌ Failed to save {service['name']} credentials")
    
    def _handle_validate_credentials(self, service_key: str, service: Dict[str, Any], 
                                   credential_data: Dict[str, str]):
        """Handle validating credentials."""
        if not service.get('validation_url'):
            st.warning(f"Validation not available for {service['name']}")
            return
        
        if not credential_data:
            st.error("Please enter credentials before validating")
            return
        
        with st.spinner(f"Validating {service['name']} credentials..."):
            is_valid = asyncio.run(self._validate_api_credentials(service_key, service, credential_data))
            
            if is_valid:
                st.success(f"✅ {service['name']} credentials are valid!")
                
                # Update validation status in database
                user_email = st.session_state.onboarding_data.get('email', '')
                if user_email:
                    # Save with validation status
                    db_manager.save_credential(user_email, service_key, service['type'], credential_data)
                    # TODO: Update validation status in database
                    
            else:
                st.error(f"❌ {service['name']} credentials are invalid or expired")
    
    def _handle_delete_credentials(self, service_key: str, service: Dict[str, Any]):
        """Handle deleting credentials."""
        user_email = st.session_state.onboarding_data.get('email', '')
        if not user_email:
            return
        
        # TODO: Implement delete functionality in database manager
        st.warning(f"Delete functionality for {service['name']} coming soon!")
    
    async def _validate_api_credentials(self, service_key: str, service: Dict[str, Any], 
                                      credential_data: Dict[str, str]) -> bool:
        """Validate API credentials by making a test request."""
        try:
            validation_url = service['validation_url']
            headers = {}
            
            # Set up authentication headers based on service
            if service_key == 'openai':
                headers['Authorization'] = f"Bearer {credential_data['api_key']}"
            elif service_key == 'github':
                headers['Authorization'] = f"token {credential_data['access_token']}"
            elif service_key == 'anthropic':
                headers['x-api-key'] = credential_data['api_key']
                headers['anthropic-version'] = '2023-06-01'
            
            # Make validation request
            async with httpx.AsyncClient(timeout=10.0) as client:
                if service_key == 'anthropic':
                    # Anthropic requires a POST request
                    response = await client.post(
                        validation_url,
                        headers=headers,
                        json={
                            "model": "claude-3-haiku-20240307",
                            "max_tokens": 1,
                            "messages": [{"role": "user", "content": "test"}]
                        }
                    )
                else:
                    response = await client.get(validation_url, headers=headers)
                
                return response.status_code in [200, 201]
                
        except Exception as e:
            logger.error(f"Credential validation failed for {service_key}: {e}")
            return False
    
    def _show_validation_status(self, service_key: str):
        """Show validation status for service."""
        user_email = st.session_state.onboarding_data.get('email', '')
        if not user_email:
            return
        
        # TODO: Get validation status from database
        # For now, show placeholder
        st.markdown("""
        <div style="background: #f0f2f6; padding: 1rem; border-radius: 5px; margin-top: 1rem;">
            <small><strong>Status:</strong> Validation status will be shown here after implementation</small>
        </div>
        """, unsafe_allow_html=True)
    
    def render_credentials_overview(self):
        """Render overview of all saved credentials."""
        st.subheader("🔐 Saved Credentials")
        
        user_email = st.session_state.get('user_email', '')
        if not user_email:
            st.warning("Please log in to view credentials")
            return
        
        # Get all credentials for user
        credentials_found = False
        
        for service_key, service in self.supported_services.items():
            creds = db_manager.get_credential(user_email, service_key)
            if creds:
                credentials_found = True
                
                col1, col2, col3 = st.columns([2, 1, 1])
                with col1:
                    st.write(f"{service['icon']} **{service['name']}**")
                    st.caption(service['description'])
                
                with col2:
                    # TODO: Show actual validation status
                    st.success("✅ Saved")
                
                with col3:
                    if st.button(f"Edit", key=f"edit_{service_key}"):
                        st.session_state.edit_credential = service_key
                        st.rerun()
        
        if not credentials_found:
            st.info("No credentials saved yet. Add some credentials to get started!")
    
    def get_available_services(self) -> List[str]:
        """Get list of services with valid credentials."""
        user_email = st.session_state.get('user_email', '')
        if not user_email:
            return []
        
        available = []
        for service_key in self.supported_services.keys():
            if db_manager.get_credential(user_email, service_key):
                available.append(service_key)
        
        return available
