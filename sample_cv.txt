<PERSON>
Senior Software Engineer

Email: <EMAIL>
Phone: +****************
Location: San Francisco, CA

PROFESSIONAL SUMMARY
Experienced Senior Software Engineer with 8 years of expertise in full-stack development, cloud architecture, and team leadership. Proven track record of delivering scalable web applications and leading cross-functional teams in fast-paced environments.

TECHNICAL SKILLS
• Programming Languages: Python, JavaScript, TypeScript, Java, Go
• Frontend: React, Vue.js, Angular, HTML5, CSS3, Sass
• Backend: Node.js, Django, Flask, Express.js, Spring Boot
• Databases: PostgreSQL, MySQL, MongoDB, Redis
• Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins, Terraform
• Tools: Git, GitHub, Jira, <PERSON>lack, VS Code

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechCorp Inc. | 2020 - Present
• Led development of microservices architecture serving 1M+ users
• Implemented CI/CD pipelines reducing deployment time by 60%
• Mentored 5 junior developers and conducted code reviews
• Collaborated with product managers to define technical requirements
• Technologies: <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PostgreSQL

Software Engineer | StartupXYZ | 2018 - 2020
• Developed full-stack web applications using React and Node.js
• Optimized database queries improving application performance by 40%
• Participated in agile development processes and sprint planning
• Built RESTful APIs and integrated third-party services
• Technologies: JavaScript, React, Node.js, MongoDB, Express

Junior Software Developer | WebSolutions | 2016 - 2018
• Developed responsive web applications using modern frameworks
• Collaborated with designers to implement pixel-perfect UIs
• Wrote unit tests and participated in code reviews
• Fixed bugs and implemented new features based on user feedback
• Technologies: HTML, CSS, JavaScript, PHP, MySQL

EDUCATION
Bachelor of Science in Computer Science
University of California, Berkeley | 2012 - 2016

CERTIFICATIONS
• AWS Certified Solutions Architect
• Certified Kubernetes Administrator (CKA)
• Scrum Master Certification

PROJECTS
E-commerce Platform | Personal Project
• Built a full-stack e-commerce platform with React and Node.js
• Implemented payment processing with Stripe API
• Deployed on AWS with auto-scaling capabilities

Open Source Contributions
• Contributor to popular React component library (500+ stars)
• Maintained Python package for data visualization
• Active participant in local tech meetups and conferences
