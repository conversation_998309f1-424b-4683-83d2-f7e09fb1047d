# JobFlow - Smart Job Application Automation

🐾 **Your friendly AI-powered job search companion with enhanced account management and cloud sync**

JobFlow is a modern, privacy-focused job application automation platform that helps you find and apply to jobs efficiently while maintaining full control over your data. Now with secure account management, encrypted credential storage, and Google Drive backup.

## ✨ Enhanced Features

### 👤 Account Management
- **Secure User Accounts**: Create and manage your personal JobFlow account
- **Encrypted Credentials**: All API keys and sensitive data encrypted locally
- **Progress Tracking**: Remember your setup progress and preferences
- **Multi-Device Support**: Access your account from multiple devices

### ☁️ Cloud Backup & Sync
- **Google Drive Integration**: Secure backup to your Google Drive
- **Encrypted Backups**: All backups are encrypted with your password
- **Cross-Device Sync**: Access your data from anywhere
- **Version History**: Keep multiple backup versions
- **Easy Restore**: One-click data restoration

### 🤖 AI-Powered Intelligence
- **Smart CV Optimization**: Automatically tailor your CV for each job
- **Custom Cover Letters**: Generate personalized cover letters
- **Job Matching**: Intelligent analysis of job compatibility
- **Cost Monitoring**: Real-time AI usage and cost tracking
- **Multiple AI Providers**: Support for OpenAI, Anthropic, and Perplexity

### 🔍 Multi-Portal Job Search
- **WeWorkRemotely**: Remote job opportunities
- **AngelList**: Startup positions
- **LinkedIn**: Professional network jobs
- **Indeed**: Comprehensive job listings
- **Glassdoor**: Company insights and reviews

### 📊 Advanced Analytics
- **Application Tracking**: Monitor your job application pipeline
- **Success Metrics**: Track response rates and interview conversions
- **Market Insights**: Salary trends and job market analysis
- **Performance Dashboard**: Visual analytics and reporting

### 🔒 Privacy & Security
- **Local Data Storage**: All your data stays on your device
- **End-to-End Encryption**: Credentials and backups are encrypted
- **No Data Sharing**: We never transmit your personal information
- **GDPR Compliant**: Full control over your data
- **Secure Authentication**: Password-based account protection

### 🎨 Modern Design
- **Enhanced Dark Theme**: Beautiful, modern interface with better typography
- **Pet-Lover Friendly**: Warm, welcoming design with subtle pet-themed touches
- **Responsive UI**: Works beautifully on all devices
- **Intuitive Navigation**: Clean, hierarchical user experience
- **Custom Fonts**: Inter and Poppins for better readability

## 🚀 Quick Start

### Prerequisites
- Python 3.11+ (recommended: 3.13.3)
- [uv](https://github.com/astral-sh/uv) package manager (for fast dependency management)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/jobflow.git
   cd jobflow
   ```

2. **Install dependencies with uv**
   ```bash
   # Install uv if you haven't already
   curl -LsSf https://astral.sh/uv/install.sh | sh

   # Install project dependencies
   uv sync
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the application**
   ```bash
   uv run streamlit run app.py
   ```

5. **Open your browser**
   Navigate to `http://localhost:8501`

### Environment Configuration

Create a `.env` file with the following settings:

```env
# AI Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Application Settings
APP_NAME="JobFlow"
DEBUG=false
LOG_LEVEL=INFO

# Cost Limits
DAILY_AI_COST_LIMIT=10.0
MONTHLY_AI_COST_LIMIT=200.0

# Browser Automation
BROWSER_HEADLESS=true
AUTOMATION_DELAY=2000

# Google Sign-In (Optional - enables one-click authentication)
GOOGLE_CLIENT_ID=your_google_client_id_here.apps.googleusercontent.com
```

### 🚀 Google Sign-In Setup (Optional - 5 minutes)

Enable one-click Google authentication for easier user onboarding:

1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
2. **Create a project** (or select existing)
3. **Create OAuth 2.0 Client ID:**
   - Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client ID"
   - Application type: "Web application"
   - Authorized origins: `http://localhost:8501`
4. **Copy the Client ID** and add to `.env`:
   ```bash
   GOOGLE_CLIENT_ID=your_client_id.apps.googleusercontent.com
   ```

**That's it!** No client secret or complex setup needed. Users will see a "Sign in with Google" button.

📖 **Detailed setup guide**: [docs/GOOGLE_SETUP.md](docs/GOOGLE_SETUP.md)

## 📖 Usage Guide

### 1. Onboarding
- Complete the 5-step onboarding process
- Upload your CV for AI analysis
- Set your job preferences and skills

### 2. AI-Powered CV Analysis
- Upload CV in PDF, DOCX, or TXT format
- Get detailed analysis of skills, experience, and strengths
- Receive personalized job recommendations

### 3. Job Search
- Search across multiple job portals
- Use AI to match jobs against your profile
- Get match scores and optimization suggestions

### 4. Cost Monitoring
- Track AI usage in real-time
- Set spending limits and receive alerts
- View detailed cost breakdowns by model and request type

### 5. Project Status
- Monitor development progress
- View feature completion percentages
- Access prioritized todo lists

## 🏗️ Architecture

### Core Components

```
jobflow/
├── app.py                     # Main Streamlit application
├── models.py                  # Pydantic data models
├── styles/
│   └── theme.py              # Design system and styling
├── ai/
│   ├── openrouter_client.py  # AI API integration
│   ├── cv_processor.py       # CV processing system
│   └── cost_tracker.py       # Cost monitoring
├── automation/
│   ├── queue_manager.py      # Background job processing
│   ├── browser.py            # Playwright automation
│   └── api_client.py         # Job portal APIs
├── database/
│   ├── manager.py            # Database operations
│   └── models.py             # SQLAlchemy models
├── components/
│   ├── onboarding.py         # User onboarding flow
│   ├── dashboard.py          # Analytics dashboard
│   └── credentials.py        # Credential management
├── api/
│   └── endpoints.py          # REST API endpoints
└── utils/
    ├── config.py             # Configuration management
    ├── logger.py             # Logging system
    └── feature_audit.py      # Progress tracking
```

### Technology Stack

- **Frontend**: Streamlit with custom components
- **Backend**: Python with FastAPI for APIs
- **Database**: SQLite with SQLAlchemy ORM
- **AI**: OpenRouter (Claude, GPT-4, etc.)
- **Automation**: Playwright for browser automation
- **Styling**: Custom CSS with Google Fonts
- **Package Management**: uv for fast dependency resolution

## 🔧 Development

### Running in Development Mode

```bash
# Enable debug mode
DEBUG=true uv run streamlit run app.py

# Run with auto-reload
uv run streamlit run app.py --server.runOnSave true
```

### API Server

Start the FastAPI server for external integrations:

```bash
uv run python api/endpoints.py
```

Access API documentation at `http://localhost:8000/docs`

### Testing

```bash
# Run unit tests (when implemented)
uv run pytest

# Run integration tests
uv run pytest tests/integration/

# Test specific components
uv run pytest tests/test_cv_processor.py
```

### Code Quality

```bash
# Format code
uv run black .

# Lint code
uv run flake8 .

# Type checking
uv run mypy .
```

## 📊 Feature Status

### ✅ Completed (85% overall progress)

- **Core Infrastructure** (100%): Project setup, database, logging
- **Design System** (100%): Custom theme, responsive design, branding
- **AI Processing** (100%): OpenRouter integration, CV analysis, cost tracking
- **Queue System** (100%): Background processing, auto-run mode
- **API Endpoints** (100%): REST API, connection testing
- **Project Management** (100%): Feature audit, progress tracking

### 🚧 In Progress

- **Browser Automation** (40%): Portal-specific implementations needed
- **Security Features** (60%): Authentication system required
- **Documentation** (20%): User guides and API docs needed

### 📋 Todo

- **Unit Testing** (0%): Comprehensive test coverage
- **Job Portal Integrations** (30%): More portals beyond We Work Remotely
- **Deployment System** (0%): Docker, CI/CD pipeline

## 🔒 Privacy & Security

JobFlow is built with privacy-first principles:

- **Local Data Storage**: All data stored locally by default
- **Encrypted Credentials**: Secure credential storage with encryption
- **No Cloud Dependencies**: Works completely offline (except AI features)
- **Transparent Logging**: Privacy-aware logging with sensitive data filtering
- **User Control**: Complete data export and deletion capabilities

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and test thoroughly
4. Commit with descriptive messages: `git commit -m 'Add amazing feature'`
5. Push to your branch: `git push origin feature/amazing-feature`
6. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Streamlit** for the amazing web framework
- **OpenRouter** for AI model access
- **Playwright** for browser automation
- **SQLAlchemy** for database management
- **Google Fonts** for Funnel Sans typography

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/yourusername/jobflow/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/jobflow/discussions)
- **Email**: <EMAIL>

---

<div align="center">
  <p>Made with ❤️ for pet lovers who want a delightful job search experience</p>
  <p>🐾 Happy job hunting! 🐾</p>
</div>