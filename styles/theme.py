"""
JobFlow Design System - Theme Configuration
Pet-lover friendly design with modern color palette and typography.
"""

from typing import Dict, Any
import streamlit as st

# Color Palette - Pet-lover friendly, modern design
COLORS = {
    # Primary Colors
    "primary": "#2c2f47",           # Dark blue-gray (main brand)
    "secondary": "#1a1d2e",         # Darker blue-gray (accents)
    "background": "#e3e5e8",        # Light gray (main background)
    "accent": "#7289da",            # Discord-like blue (highlights)
    
    # Extended Palette
    "primary_light": "#3d4158",     # Lighter primary
    "primary_dark": "#1f2236",      # Darker primary
    "accent_light": "#8fa3e8",      # Lighter accent
    "accent_dark": "#5b6ecc",       # Darker accent
    
    # Semantic Colors
    "success": "#4caf50",           # Green for success states
    "warning": "#ff9800",           # Orange for warnings
    "error": "#f44336",             # Red for errors
    "info": "#2196f3",              # Blue for info
    
    # Neutral Colors
    "white": "#ffffff",
    "light_gray": "#f5f5f5",
    "medium_gray": "#9e9e9e",
    "dark_gray": "#424242",
    "black": "#212121",
    
    # Pet-themed Accent Colors (subtle use)
    "warm_brown": "#8d6e63",        # Warm brown (dog-friendly)
    "soft_orange": "#ffb74d",       # Soft orange (cat-friendly)
    "gentle_green": "#81c784",      # Gentle green (nature)
}

# Typography Configuration
TYPOGRAPHY = {
    "font_family": "Funnel Sans",
    "font_weights": {
        "light": 300,
        "regular": 400,
        "medium": 500,
        "semibold": 600,
        "bold": 700,
    },
    "font_sizes": {
        "xs": "0.75rem",    # 12px
        "sm": "0.875rem",   # 14px
        "base": "1rem",     # 16px
        "lg": "1.125rem",   # 18px
        "xl": "1.25rem",    # 20px
        "2xl": "1.5rem",    # 24px
        "3xl": "1.875rem",  # 30px
        "4xl": "2.25rem",   # 36px
    }
}

# Component Styles
COMPONENT_STYLES = {
    "card": {
        "background": COLORS["white"],
        "border": f"1px solid {COLORS['light_gray']}",
        "border_radius": "12px",
        "box_shadow": "0 2px 8px rgba(44, 47, 71, 0.1)",
        "padding": "1.5rem",
    },
    "button_primary": {
        "background": COLORS["accent"],
        "color": COLORS["white"],
        "border": "none",
        "border_radius": "8px",
        "padding": "0.75rem 1.5rem",
        "font_weight": TYPOGRAPHY["font_weights"]["medium"],
        "transition": "all 0.2s ease",
    },
    "button_secondary": {
        "background": "transparent",
        "color": COLORS["primary"],
        "border": f"2px solid {COLORS['primary']}",
        "border_radius": "8px",
        "padding": "0.75rem 1.5rem",
        "font_weight": TYPOGRAPHY["font_weights"]["medium"],
    },
    "input": {
        "border": f"2px solid {COLORS['light_gray']}",
        "border_radius": "8px",
        "padding": "0.75rem",
        "font_size": TYPOGRAPHY["font_sizes"]["base"],
        "transition": "border-color 0.2s ease",
    },
    "metric_card": {
        "background": f"linear-gradient(135deg, {COLORS['white']} 0%, {COLORS['light_gray']} 100%)",
        "border": f"1px solid {COLORS['medium_gray']}",
        "border_radius": "16px",
        "padding": "2rem",
        "text_align": "center",
    }
}

# Layout Configuration
LAYOUT = {
    "container_max_width": "1200px",
    "sidebar_width": "280px",
    "spacing": {
        "xs": "0.25rem",
        "sm": "0.5rem",
        "md": "1rem",
        "lg": "1.5rem",
        "xl": "2rem",
        "2xl": "3rem",
    },
    "breakpoints": {
        "mobile": "768px",
        "tablet": "1024px",
        "desktop": "1200px",
    }
}

# Animation Configuration
ANIMATIONS = {
    "transition_fast": "0.15s ease",
    "transition_normal": "0.2s ease",
    "transition_slow": "0.3s ease",
    "hover_scale": "1.02",
    "hover_shadow": "0 4px 16px rgba(44, 47, 71, 0.15)",
}

def get_custom_css() -> str:
    """Generate custom CSS for the application."""
    return f"""
    <style>
    /* Import Funnel Sans font from Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Funnel+Sans:wght@300;400;500;600;700&display=swap');

    /* Global Dark Theme Styles */
    .stApp {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif;
    }}

    /* Main content area */
    .main .block-container {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        padding-top: 2rem;
    }}

    /* Hide Streamlit branding */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}

    /* Dark theme for all text elements */
    .stMarkdown, .stText, p, h1, h2, h3, h4, h5, h6, span, div {{
        color: {COLORS['white']} !important;
    }}

    /* Sidebar dark theme */
    .css-1d391kg {{
        background-color: {COLORS['secondary']} !important;
    }}

    .css-1d391kg .css-1v0mbdj {{
        color: {COLORS['white']} !important;
    }}

    /* Content area background */
    .css-18e3th9 {{
        background-color: {COLORS['primary']} !important;
    }}

    /* Main container */
    .css-1y4p8pa {{
        background-color: {COLORS['primary']} !important;
    }}
    
    /* Custom container styles */
    .main-container {{
        max-width: {LAYOUT['container_max_width']};
        margin: 0 auto;
        padding: {LAYOUT['spacing']['lg']};
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
    }}

    /* Card styles - Dark theme */
    .custom-card {{
        background: {COLORS['secondary']} !important;
        border: 1px solid {COLORS['accent']} !important;
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        padding: {COMPONENT_STYLES['card']['padding']};
        margin-bottom: {LAYOUT['spacing']['lg']};
        transition: {ANIMATIONS['transition_normal']};
        color: {COLORS['white']} !important;
    }}

    .custom-card:hover {{
        transform: scale({ANIMATIONS['hover_scale']});
        box-shadow: 0 6px 20px rgba(114, 137, 218, 0.3) !important;
        border-color: {COLORS['accent_light']} !important;
    }}

    /* File uploader dark theme */
    .stFileUploader {{
        background-color: {COLORS['secondary']} !important;
        border: 2px dashed {COLORS['accent']} !important;
        border-radius: 12px !important;
        padding: 2rem !important;
    }}

    .stFileUploader > div {{
        background-color: transparent !important;
        color: {COLORS['white']} !important;
    }}

    .stFileUploader label {{
        color: {COLORS['white']} !important;
    }}

    /* Upload area styling */
    [data-testid="stFileUploader"] {{
        background-color: {COLORS['secondary']} !important;
        border: 2px dashed {COLORS['accent']} !important;
        border-radius: 12px !important;
    }}

    [data-testid="stFileUploader"] > div {{
        background-color: transparent !important;
    }}

    [data-testid="stFileUploader"] label {{
        color: {COLORS['white']} !important;
    }}
    
    /* Button styles - Dark theme */
    .stButton > button {{
        background: {COLORS['accent']} !important;
        color: {COLORS['white']} !important;
        border: none !important;
        border-radius: {COMPONENT_STYLES['button_primary']['border_radius']} !important;
        padding: {COMPONENT_STYLES['button_primary']['padding']} !important;
        font-weight: {COMPONENT_STYLES['button_primary']['font_weight']} !important;
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif !important;
        transition: {COMPONENT_STYLES['button_primary']['transition']} !important;
        width: 100% !important;
    }}

    .stButton > button:hover {{
        background: {COLORS['accent_dark']} !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(114, 137, 218, 0.4) !important;
    }}

    /* Secondary button styles */
    .stButton > button[kind="secondary"] {{
        background: transparent !important;
        color: {COLORS['accent']} !important;
        border: 2px solid {COLORS['accent']} !important;
    }}

    .stButton > button[kind="secondary"]:hover {{
        background: {COLORS['accent']} !important;
        color: {COLORS['white']} !important;
    }}
    
    /* Input styles - Dark theme */
    .stTextInput > div > div > input,
    .stTextArea > div > div > textarea,
    .stSelectbox > div > div > select,
    .stNumberInput > div > div > input {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
        border: 2px solid {COLORS['medium_gray']} !important;
        border-radius: {COMPONENT_STYLES['input']['border_radius']} !important;
        padding: {COMPONENT_STYLES['input']['padding']} !important;
        font-size: {COMPONENT_STYLES['input']['font_size']} !important;
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif !important;
        transition: {COMPONENT_STYLES['input']['transition']} !important;
    }}

    .stTextInput > div > div > input:focus,
    .stTextArea > div > div > textarea:focus,
    .stSelectbox > div > div > select:focus,
    .stNumberInput > div > div > input:focus {{
        border-color: {COLORS['accent']} !important;
        box-shadow: 0 0 0 3px rgba(114, 137, 218, 0.2) !important;
        background-color: {COLORS['secondary']} !important;
    }}

    /* Input labels */
    .stTextInput > label,
    .stTextArea > label,
    .stSelectbox > label,
    .stNumberInput > label,
    .stFileUploader > label {{
        color: {COLORS['white']} !important;
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif !important;
    }}
    
    /* Metric card styles */
    .metric-card {{
        background: {COMPONENT_STYLES['metric_card']['background']};
        border: {COMPONENT_STYLES['metric_card']['border']};
        border-radius: {COMPONENT_STYLES['metric_card']['border_radius']};
        padding: {COMPONENT_STYLES['metric_card']['padding']};
        text-align: {COMPONENT_STYLES['metric_card']['text_align']};
        transition: {ANIMATIONS['transition_normal']};
    }}
    
    .metric-card:hover {{
        transform: scale({ANIMATIONS['hover_scale']});
        box-shadow: {ANIMATIONS['hover_shadow']};
    }}
    
    /* Header styles - Dark theme */
    .main-header {{
        color: {COLORS['white']} !important;
        font-size: {TYPOGRAPHY['font_sizes']['4xl']} !important;
        font-weight: {TYPOGRAPHY['font_weights']['bold']} !important;
        text-align: center !important;
        margin-bottom: {LAYOUT['spacing']['2xl']} !important;
        background: linear-gradient(135deg, {COLORS['accent']} 0%, {COLORS['accent_light']} 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }}

    .section-header {{
        color: {COLORS['white']} !important;
        font-size: {TYPOGRAPHY['font_sizes']['2xl']} !important;
        font-weight: {TYPOGRAPHY['font_weights']['semibold']} !important;
        margin-bottom: {LAYOUT['spacing']['lg']} !important;
        border-bottom: 2px solid {COLORS['accent']} !important;
        padding-bottom: {LAYOUT['spacing']['sm']} !important;
    }}

    /* Streamlit headers */
    h1, h2, h3, h4, h5, h6 {{
        color: {COLORS['white']} !important;
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif !important;
    }}

    /* Welcome message styling */
    .welcome-title {{
        color: {COLORS['white']} !important;
        font-size: 3rem !important;
        font-weight: 700 !important;
        text-align: center !important;
        margin-bottom: 1rem !important;
        background: linear-gradient(135deg, {COLORS['accent']} 0%, {COLORS['accent_light']} 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }}

    .welcome-subtitle {{
        color: {COLORS['light_gray']} !important;
        font-size: 1.2rem !important;
        text-align: center !important;
        margin-bottom: 2rem !important;
    }}

    .welcome-description {{
        color: {COLORS['medium_gray']} !important;
        font-size: 1rem !important;
        text-align: center !important;
        margin-bottom: 3rem !important;
    }}
    
    /* Sidebar styles */
    .css-1d391kg {{
        background-color: {COLORS['primary']};
    }}
    
    .css-1d391kg .css-1v0mbdj {{
        color: {COLORS['white']};
    }}
    
    /* Success/Error message styles */
    .stSuccess {{
        background-color: {COLORS['success']};
        color: {COLORS['white']};
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
    }}
    
    .stError {{
        background-color: {COLORS['error']};
        color: {COLORS['white']};
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
    }}
    
    .stWarning {{
        background-color: {COLORS['warning']};
        color: {COLORS['white']};
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
    }}
    
    .stInfo {{
        background-color: {COLORS['info']};
        color: {COLORS['white']};
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
    }}
    
    /* Pet-friendly touches */
    .pet-accent {{
        color: {COLORS['warm_brown']};
    }}
    
    .gift-like-border {{
        border: 3px solid {COLORS['accent']};
        border-radius: 16px;
        background: linear-gradient(135deg, {COLORS['white']} 0%, {COLORS['light_gray']} 100%);
        position: relative;
    }}
    
    .gift-like-border::before {{
        content: '';
        position: absolute;
        top: -3px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 6px;
        background: {COLORS['accent']};
        border-radius: 3px;
    }}
    
    /* Progress bars */
    .stProgress > div > div > div {{
        background-color: {COLORS['accent']} !important;
    }}

    .stProgress > div > div {{
        background-color: {COLORS['medium_gray']} !important;
    }}

    /* Expanders */
    .streamlit-expanderHeader {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
        border: 1px solid {COLORS['accent']} !important;
        border-radius: 8px !important;
    }}

    .streamlit-expanderContent {{
        background-color: {COLORS['primary']} !important;
        color: {COLORS['white']} !important;
        border: 1px solid {COLORS['medium_gray']} !important;
        border-top: none !important;
    }}

    /* Dataframes */
    .stDataFrame {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
    }}

    /* Tabs */
    .stTabs [data-baseweb="tab-list"] {{
        background-color: {COLORS['secondary']} !important;
    }}

    .stTabs [data-baseweb="tab"] {{
        background-color: transparent !important;
        color: {COLORS['white']} !important;
        border-bottom: 2px solid transparent !important;
    }}

    .stTabs [data-baseweb="tab"]:hover {{
        background-color: {COLORS['primary']} !important;
        color: {COLORS['accent']} !important;
    }}

    .stTabs [aria-selected="true"] {{
        background-color: {COLORS['primary']} !important;
        color: {COLORS['accent']} !important;
        border-bottom: 2px solid {COLORS['accent']} !important;
    }}

    /* Columns and containers */
    .element-container {{
        background-color: transparent !important;
    }}

    /* Multiselect */
    .stMultiSelect > div > div {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
        border: 2px solid {COLORS['medium_gray']} !important;
    }}

    .stMultiSelect > div > div > div {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
    }}

    /* Slider */
    .stSlider > div > div > div > div {{
        background-color: {COLORS['accent']} !important;
    }}

    /* Checkbox */
    .stCheckbox > label {{
        color: {COLORS['white']} !important;
    }}

    /* Radio */
    .stRadio > label {{
        color: {COLORS['white']} !important;
    }}

    /* Spinner */
    .stSpinner > div {{
        border-top-color: {COLORS['accent']} !important;
    }}

    /* Responsive design */
    @media (max-width: {LAYOUT['breakpoints']['mobile']}) {{
        .main-container {{
            padding: {LAYOUT['spacing']['md']};
        }}

        .main-header {{
            font-size: {TYPOGRAPHY['font_sizes']['3xl']};
        }}

        .section-header {{
            font-size: {TYPOGRAPHY['font_sizes']['xl']};
        }}

        .welcome-title {{
            font-size: 2rem !important;
        }}

        .welcome-subtitle {{
            font-size: 1rem !important;
        }}
    }}
    </style>
    """

def apply_theme():
    """Apply the custom theme to the Streamlit app."""
    st.markdown(get_custom_css(), unsafe_allow_html=True)

def get_color(color_name: str) -> str:
    """Get a color from the theme palette."""
    return COLORS.get(color_name, COLORS['primary'])

def get_component_style(component_name: str) -> Dict[str, Any]:
    """Get component styles from the theme."""
    return COMPONENT_STYLES.get(component_name, {})
