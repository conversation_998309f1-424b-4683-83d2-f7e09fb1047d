# 🚀 Simple Google Sign-In Setup

Enable one-click Google authentication for your JobFlow users with this simple setup.

## 🎯 What This Enables

- **One-Click Sign-In**: Users can sign in with their Google account instantly
- **No Complex Forms**: Automatic account creation with Google profile info
- **Optional Drive Backup**: Users can enable Google Drive sync later if they want
- **Better UX**: Much simpler than creating local accounts

## 🔧 Setup Instructions (5 minutes)

### 1. Go to Google Cloud Console
Visit [Google Cloud Console](https://console.cloud.google.com/)

### 2. Create or Select Project
- **New project**: Click "New Project" and give it a name like "JobFlow"
- **Existing project**: Select your existing project

### 3. Enable Google Identity (Usually Already Enabled)
- Go to "APIs & Services" → "Library"
- Search for "Google Identity" (it's usually enabled by default)
- If not enabled, click "Enable"

### 4. Create OAuth 2.0 Credentials
1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth 2.0 Client ID"
3. If prompted, configure the OAuth consent screen:
   - User Type: "External" (for public use)
   - App name: "JobFlow"
   - User support email: Your email
   - Developer contact: Your email
   - Save and continue through the scopes (no scopes needed for basic auth)
4. Create OAuth 2.0 Client ID:
   - Application type: **"Web application"**
   - Name: "JobFlow Web Client"
   - Authorized JavaScript origins: `http://localhost:8501`
   - Authorized redirect URIs: `http://localhost:8501` (optional for this setup)

### 5. Copy Your Client ID
- Copy the Client ID (looks like: `123456789-abc123.apps.googleusercontent.com`)
- **Note**: You don't need the Client Secret for this simple setup!

### 6. Add to Your Environment
Add to your `.env` file:
```env
# Google Sign-In (Optional - enables one-click authentication)
GOOGLE_CLIENT_ID=your_client_id_here.apps.googleusercontent.com
```

### 7. Restart JobFlow
```bash
uv run streamlit run app.py
```

## ✅ That's It!

Users will now see a "Sign in with Google" button during onboarding. If they choose it:

1. **One-click sign-in** with Google popup
2. **Automatic account creation** with their Google profile
3. **Immediate access** to JobFlow features
4. **Optional Drive backup** can be enabled later in settings

## 🔒 Security Notes

- **Client ID is public**: It's safe to include in frontend code
- **No secrets needed**: This setup doesn't require a client secret
- **Secure by design**: Google handles all authentication
- **Local data**: User data is still stored locally by default

## 🛠️ For Production Deployment

When deploying to production:

1. **Update authorized origins** in Google Cloud Console:
   - Add your production domain: `https://yourdomain.com`
   - Keep localhost for development: `http://localhost:8501`

2. **Update environment variable**:
   ```env
   GOOGLE_CLIENT_ID=your_client_id_here.apps.googleusercontent.com
   ```

## 🆘 Troubleshooting

### "Client ID not configured" Warning
- Make sure `GOOGLE_CLIENT_ID` is in your `.env` file
- Restart the Streamlit app after adding the environment variable

### Google Sign-In Button Not Appearing
- Check browser console for JavaScript errors
- Ensure the Client ID is correct
- Try in an incognito window

### "Origin not allowed" Error
- Add `http://localhost:8501` to authorized origins in Google Cloud Console
- For production, add your production domain

### Users Can't Sign In
- Check that the OAuth consent screen is configured
- Ensure the app is not in "Testing" mode (or add test users)
- Verify the Client ID is correct

## 💡 Benefits of This Approach

### For Users:
- **Instant setup**: No forms to fill out
- **Familiar flow**: Everyone knows "Sign in with Google"
- **Secure**: Google handles password security
- **Optional features**: Can enable Drive backup later

### For Developers:
- **Simple setup**: Just need a Client ID
- **No backend**: No need to handle OAuth flows
- **Secure**: No secrets to manage
- **Scalable**: Works for any number of users

### For Privacy:
- **Local first**: Data still stored locally by default
- **User choice**: Google sign-in is optional
- **No tracking**: We don't get access to Google data beyond basic profile
- **Transparent**: Users know exactly what we access

---

**Need help?** Open an issue on GitHub or check the [main documentation](../README.md).
