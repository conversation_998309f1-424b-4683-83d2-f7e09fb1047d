"""
JobFlow Design System - Theme Configuration
Pet-lover friendly design with modern color palette and typography.
"""

from typing import Dict, Any
import streamlit as st

# Enhanced Color Palette - Pet-lover friendly, modern dark design
COLORS = {
    # Primary Background Colors (Dark Theme)
    "background": "#0f1419",        # Deep dark background
    "surface": "#1e2328",           # Card/component surfaces
    "elevated": "#2a2f3a",          # Elevated components (modals, dropdowns)
    "primary": "#2c2f47",           # Primary brand color
    "secondary": "#1a1d2e",         # Secondary surfaces

    # Accent Colors
    "accent": "#7289da",            # Primary accent (Discord-inspired)
    "accent_light": "#8ea1e1",      # Lighter accent for hover states
    "accent_dark": "#5b6ecd",       # Darker accent for active states
    "accent_subtle": "#4c5f99",     # Subtle accent for borders

    # Text Colors (Dark Theme)
    "text_primary": "#ffffff",      # Primary white text
    "text_secondary": "#a6adc8",    # Secondary light text
    "text_muted": "#6c7086",        # Muted/disabled text
    "text_accent": "#7289da",       # Accent text color
    "text_inverse": "#1a1d2e",      # Dark text on light backgrounds

    # Semantic Colors (Enhanced)
    "success": "#00d4aa",           # Modern teal green
    "success_light": "#26d0ce",     # Light success
    "warning": "#ffb020",           # Warm orange
    "warning_light": "#ffc947",     # Light warning
    "error": "#ff6b6b",             # Soft red
    "error_light": "#ff8787",       # Light error
    "info": "#4dabf7",              # Modern blue
    "info_light": "#74c0fc",        # Light info

    # Interactive States
    "border": "#313244",            # Default borders
    "border_light": "#45475a",      # Light borders
    "border_focus": "#7289da",      # Focused borders
    "hover": "#45475a",             # Hover backgrounds
    "active": "#585b70",            # Active/pressed states
    "disabled": "#6c7086",          # Disabled elements

    # Gradient Colors
    "gradient_primary": "linear-gradient(135deg, #7289da 0%, #a6adc8 100%)",
    "gradient_success": "linear-gradient(135deg, #00d4aa 0%, #26d0ce 100%)",
    "gradient_warning": "linear-gradient(135deg, #ffb020 0%, #ffc947 100%)",
    "gradient_surface": "linear-gradient(135deg, #2a2f3a 0%, #1e2328 100%)",

    # Pet-themed Accent Colors (Subtle, warm touches)
    "pet_warm": "#d4a574",          # Warm golden (dog-friendly)
    "pet_soft": "#e8b4cb",          # Soft pink (cat-friendly)
    "pet_nature": "#a3d977",        # Nature green (outdoor pets)
    "pet_cozy": "#c9a96e",          # Cozy brown (home comfort)

    # Special Effects
    "shadow": "rgba(0, 0, 0, 0.4)",     # Drop shadows
    "shadow_light": "rgba(0, 0, 0, 0.2)", # Light shadows
    "glow": "rgba(114, 137, 218, 0.3)",   # Accent glow effect
    "overlay": "rgba(15, 20, 25, 0.8)",   # Modal overlays
}

# Enhanced Typography Configuration
TYPOGRAPHY = {
    # Font Families
    "font_primary": "Inter",        # Primary font (clean, modern)
    "font_secondary": "JetBrains Mono", # Monospace for code
    "font_display": "Poppins",      # Display font for headers

    # Font Weights
    "font_weights": {
        "thin": 100,
        "light": 300,
        "regular": 400,
        "medium": 500,
        "semibold": 600,
        "bold": 700,
        "extrabold": 800,
        "black": 900,
    },

    # Font Sizes (Responsive scale)
    "font_sizes": {
        "xs": "0.75rem",      # 12px - Small labels, captions
        "sm": "0.875rem",     # 14px - Body text, form labels
        "base": "1rem",       # 16px - Default body text
        "lg": "1.125rem",     # 18px - Large body text
        "xl": "1.25rem",      # 20px - Small headings
        "2xl": "1.5rem",      # 24px - Medium headings
        "3xl": "1.875rem",    # 30px - Large headings
        "4xl": "2.25rem",     # 36px - Extra large headings
        "5xl": "3rem",        # 48px - Display headings
        "6xl": "3.75rem",     # 60px - Hero headings
    },

    # Line Heights
    "line_heights": {
        "tight": 1.25,
        "snug": 1.375,
        "normal": 1.5,
        "relaxed": 1.625,
        "loose": 2,
    },

    # Letter Spacing
    "letter_spacing": {
        "tighter": "-0.05em",
        "tight": "-0.025em",
        "normal": "0em",
        "wide": "0.025em",
        "wider": "0.05em",
        "widest": "0.1em",
    }
}

# Component Styles
COMPONENT_STYLES = {
    "card": {
        "background": COLORS["white"],
        "border": f"1px solid {COLORS['light_gray']}",
        "border_radius": "12px",
        "box_shadow": "0 2px 8px rgba(44, 47, 71, 0.1)",
        "padding": "1.5rem",
    },
    "button_primary": {
        "background": COLORS["accent"],
        "color": COLORS["white"],
        "border": "none",
        "border_radius": "8px",
        "padding": "0.75rem 1.5rem",
        "font_weight": TYPOGRAPHY["font_weights"]["medium"],
        "transition": "all 0.2s ease",
    },
    "button_secondary": {
        "background": "transparent",
        "color": COLORS["primary"],
        "border": f"2px solid {COLORS['primary']}",
        "border_radius": "8px",
        "padding": "0.75rem 1.5rem",
        "font_weight": TYPOGRAPHY["font_weights"]["medium"],
    },
    "input": {
        "border": f"2px solid {COLORS['light_gray']}",
        "border_radius": "8px",
        "padding": "0.75rem",
        "font_size": TYPOGRAPHY["font_sizes"]["base"],
        "transition": "border-color 0.2s ease",
    },
    "metric_card": {
        "background": f"linear-gradient(135deg, {COLORS['white']} 0%, {COLORS['light_gray']} 100%)",
        "border": f"1px solid {COLORS['medium_gray']}",
        "border_radius": "16px",
        "padding": "2rem",
        "text_align": "center",
    }
}

# Layout Configuration
LAYOUT = {
    "container_max_width": "1200px",
    "sidebar_width": "280px",
    "spacing": {
        "xs": "0.25rem",
        "sm": "0.5rem",
        "md": "1rem",
        "lg": "1.5rem",
        "xl": "2rem",
        "2xl": "3rem",
    },
    "breakpoints": {
        "mobile": "768px",
        "tablet": "1024px",
        "desktop": "1200px",
    }
}

# Animation Configuration
ANIMATIONS = {
    "transition_fast": "0.15s ease",
    "transition_normal": "0.2s ease",
    "transition_slow": "0.3s ease",
    "hover_scale": "1.02",
    "hover_shadow": "0 4px 16px rgba(44, 47, 71, 0.15)",
}

def get_custom_css() -> str:
    """Generate custom CSS for the application."""
    return f"""
    <style>
    /* Import Funnel Sans font from Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Funnel+Sans:wght@300;400;500;600;700&display=swap');

    /* Global Dark Theme Styles */
    .stApp {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif;
    }}

    /* Main content area */
    .main .block-container {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        padding-top: 2rem;
    }}

    /* Hide Streamlit branding */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}

    /* Dark theme for all text elements */
    .stMarkdown, .stText, p, h1, h2, h3, h4, h5, h6, span, div {{
        color: {COLORS['white']} !important;
    }}

    /* Sidebar dark theme */
    .css-1d391kg {{
        background-color: {COLORS['secondary']} !important;
    }}

    .css-1d391kg .css-1v0mbdj {{
        color: {COLORS['white']} !important;
    }}

    /* Content area background */
    .css-18e3th9 {{
        background-color: {COLORS['primary']} !important;
    }}

    /* Main container */
    .css-1y4p8pa {{
        background-color: {COLORS['primary']} !important;
    }}
    
    /* Custom container styles */
    .main-container {{
        max-width: {LAYOUT['container_max_width']};
        margin: 0 auto;
        padding: {LAYOUT['spacing']['lg']};
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
    }}

    /* Card styles - Dark theme */
    .custom-card {{
        background: {COLORS['secondary']} !important;
        border: 1px solid {COLORS['accent']} !important;
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        padding: {COMPONENT_STYLES['card']['padding']};
        margin-bottom: {LAYOUT['spacing']['lg']};
        transition: {ANIMATIONS['transition_normal']};
        color: {COLORS['white']} !important;
    }}

    .custom-card:hover {{
        transform: scale({ANIMATIONS['hover_scale']});
        box-shadow: 0 6px 20px rgba(114, 137, 218, 0.3) !important;
        border-color: {COLORS['accent_light']} !important;
    }}

    /* File uploader dark theme */
    .stFileUploader {{
        background-color: {COLORS['secondary']} !important;
        border: 2px dashed {COLORS['accent']} !important;
        border-radius: 12px !important;
        padding: 2rem !important;
    }}

    .stFileUploader > div {{
        background-color: transparent !important;
        color: {COLORS['white']} !important;
    }}

    .stFileUploader label {{
        color: {COLORS['white']} !important;
    }}

    /* Upload area styling */
    [data-testid="stFileUploader"] {{
        background-color: {COLORS['secondary']} !important;
        border: 2px dashed {COLORS['accent']} !important;
        border-radius: 12px !important;
    }}

    [data-testid="stFileUploader"] > div {{
        background-color: transparent !important;
    }}

    [data-testid="stFileUploader"] label {{
        color: {COLORS['white']} !important;
    }}
    
    /* Button styles - Dark theme */
    .stButton > button {{
        background: {COLORS['accent']} !important;
        color: {COLORS['white']} !important;
        border: none !important;
        border-radius: {COMPONENT_STYLES['button_primary']['border_radius']} !important;
        padding: {COMPONENT_STYLES['button_primary']['padding']} !important;
        font-weight: {COMPONENT_STYLES['button_primary']['font_weight']} !important;
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif !important;
        transition: {COMPONENT_STYLES['button_primary']['transition']} !important;
        width: 100% !important;
    }}

    .stButton > button:hover {{
        background: {COLORS['accent_dark']} !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(114, 137, 218, 0.4) !important;
    }}

    /* Secondary button styles */
    .stButton > button[kind="secondary"] {{
        background: transparent !important;
        color: {COLORS['accent']} !important;
        border: 2px solid {COLORS['accent']} !important;
    }}

    .stButton > button[kind="secondary"]:hover {{
        background: {COLORS['accent']} !important;
        color: {COLORS['white']} !important;
    }}
    
    /* Input styles - Dark theme */
    .stTextInput > div > div > input,
    .stTextArea > div > div > textarea,
    .stSelectbox > div > div > select,
    .stNumberInput > div > div > input {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
        border: 2px solid {COLORS['medium_gray']} !important;
        border-radius: {COMPONENT_STYLES['input']['border_radius']} !important;
        padding: {COMPONENT_STYLES['input']['padding']} !important;
        font-size: {COMPONENT_STYLES['input']['font_size']} !important;
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif !important;
        transition: {COMPONENT_STYLES['input']['transition']} !important;
    }}

    .stTextInput > div > div > input:focus,
    .stTextArea > div > div > textarea:focus,
    .stSelectbox > div > div > select:focus,
    .stNumberInput > div > div > input:focus {{
        border-color: {COLORS['accent']} !important;
        box-shadow: 0 0 0 3px rgba(114, 137, 218, 0.2) !important;
        background-color: {COLORS['secondary']} !important;
    }}

    /* Input labels */
    .stTextInput > label,
    .stTextArea > label,
    .stSelectbox > label,
    .stNumberInput > label,
    .stFileUploader > label {{
        color: {COLORS['white']} !important;
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif !important;
    }}
    
    /* Metric card styles */
    .metric-card {{
        background: {COMPONENT_STYLES['metric_card']['background']};
        border: {COMPONENT_STYLES['metric_card']['border']};
        border-radius: {COMPONENT_STYLES['metric_card']['border_radius']};
        padding: {COMPONENT_STYLES['metric_card']['padding']};
        text-align: {COMPONENT_STYLES['metric_card']['text_align']};
        transition: {ANIMATIONS['transition_normal']};
    }}
    
    .metric-card:hover {{
        transform: scale({ANIMATIONS['hover_scale']});
        box-shadow: {ANIMATIONS['hover_shadow']};
    }}
    
    /* Header styles - Dark theme */
    .main-header {{
        color: {COLORS['white']} !important;
        font-size: {TYPOGRAPHY['font_sizes']['4xl']} !important;
        font-weight: {TYPOGRAPHY['font_weights']['bold']} !important;
        text-align: center !important;
        margin-bottom: {LAYOUT['spacing']['2xl']} !important;
        background: linear-gradient(135deg, {COLORS['accent']} 0%, {COLORS['accent_light']} 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }}

    .section-header {{
        color: {COLORS['white']} !important;
        font-size: {TYPOGRAPHY['font_sizes']['2xl']} !important;
        font-weight: {TYPOGRAPHY['font_weights']['semibold']} !important;
        margin-bottom: {LAYOUT['spacing']['lg']} !important;
        border-bottom: 2px solid {COLORS['accent']} !important;
        padding-bottom: {LAYOUT['spacing']['sm']} !important;
    }}

    /* Streamlit headers */
    h1, h2, h3, h4, h5, h6 {{
        color: {COLORS['white']} !important;
        font-family: '{TYPOGRAPHY['font_family']}', sans-serif !important;
    }}

    /* Welcome message styling */
    .welcome-title {{
        color: {COLORS['white']} !important;
        font-size: 3rem !important;
        font-weight: 700 !important;
        text-align: center !important;
        margin-bottom: 1rem !important;
        background: linear-gradient(135deg, {COLORS['accent']} 0%, {COLORS['accent_light']} 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }}

    .welcome-subtitle {{
        color: {COLORS['light_gray']} !important;
        font-size: 1.2rem !important;
        text-align: center !important;
        margin-bottom: 2rem !important;
    }}

    .welcome-description {{
        color: {COLORS['medium_gray']} !important;
        font-size: 1rem !important;
        text-align: center !important;
        margin-bottom: 3rem !important;
    }}
    
    /* Sidebar styles */
    .css-1d391kg {{
        background-color: {COLORS['primary']};
    }}
    
    .css-1d391kg .css-1v0mbdj {{
        color: {COLORS['white']};
    }}
    
    /* Success/Error message styles */
    .stSuccess {{
        background-color: {COLORS['success']};
        color: {COLORS['white']};
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
    }}
    
    .stError {{
        background-color: {COLORS['error']};
        color: {COLORS['white']};
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
    }}
    
    .stWarning {{
        background-color: {COLORS['warning']};
        color: {COLORS['white']};
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
    }}
    
    .stInfo {{
        background-color: {COLORS['info']};
        color: {COLORS['white']};
        border-radius: {COMPONENT_STYLES['card']['border_radius']};
    }}
    
    /* Pet-friendly touches */
    .pet-accent {{
        color: {COLORS['warm_brown']};
    }}
    
    .gift-like-border {{
        border: 3px solid {COLORS['accent']};
        border-radius: 16px;
        background: linear-gradient(135deg, {COLORS['white']} 0%, {COLORS['light_gray']} 100%);
        position: relative;
    }}
    
    .gift-like-border::before {{
        content: '';
        position: absolute;
        top: -3px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 6px;
        background: {COLORS['accent']};
        border-radius: 3px;
    }}
    
    /* Progress bars */
    .stProgress > div > div > div {{
        background-color: {COLORS['accent']} !important;
    }}

    .stProgress > div > div {{
        background-color: {COLORS['medium_gray']} !important;
    }}

    /* Expanders */
    .streamlit-expanderHeader {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
        border: 1px solid {COLORS['accent']} !important;
        border-radius: 8px !important;
    }}

    .streamlit-expanderContent {{
        background-color: {COLORS['primary']} !important;
        color: {COLORS['white']} !important;
        border: 1px solid {COLORS['medium_gray']} !important;
        border-top: none !important;
    }}

    /* Dataframes */
    .stDataFrame {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
    }}

    /* Tabs */
    .stTabs [data-baseweb="tab-list"] {{
        background-color: {COLORS['secondary']} !important;
    }}

    .stTabs [data-baseweb="tab"] {{
        background-color: transparent !important;
        color: {COLORS['white']} !important;
        border-bottom: 2px solid transparent !important;
    }}

    .stTabs [data-baseweb="tab"]:hover {{
        background-color: {COLORS['primary']} !important;
        color: {COLORS['accent']} !important;
    }}

    .stTabs [aria-selected="true"] {{
        background-color: {COLORS['primary']} !important;
        color: {COLORS['accent']} !important;
        border-bottom: 2px solid {COLORS['accent']} !important;
    }}

    /* Columns and containers */
    .element-container {{
        background-color: transparent !important;
    }}

    /* Multiselect */
    .stMultiSelect > div > div {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
        border: 2px solid {COLORS['medium_gray']} !important;
    }}

    .stMultiSelect > div > div > div {{
        background-color: {COLORS['secondary']} !important;
        color: {COLORS['white']} !important;
    }}

    /* Slider */
    .stSlider > div > div > div > div {{
        background-color: {COLORS['accent']} !important;
    }}

    /* Checkbox */
    .stCheckbox > label {{
        color: {COLORS['white']} !important;
    }}

    /* Radio */
    .stRadio > label {{
        color: {COLORS['white']} !important;
    }}

    /* Spinner */
    .stSpinner > div {{
        border-top-color: {COLORS['accent']} !important;
    }}

    /* Responsive design */
    @media (max-width: {LAYOUT['breakpoints']['mobile']}) {{
        .main-container {{
            padding: {LAYOUT['spacing']['md']};
        }}

        .main-header {{
            font-size: {TYPOGRAPHY['font_sizes']['3xl']};
        }}

        .section-header {{
            font-size: {TYPOGRAPHY['font_sizes']['xl']};
        }}

        .welcome-title {{
            font-size: 2rem !important;
        }}

        .welcome-subtitle {{
            font-size: 1rem !important;
        }}
    }}
    </style>
    """

def apply_theme():
    """Apply the custom theme to the Streamlit app."""
    st.markdown(get_custom_css(), unsafe_allow_html=True)

def get_color(color_name: str) -> str:
    """Get a color from the theme palette."""
    return COLORS.get(color_name, COLORS['primary'])

def get_component_style(component_name: str) -> Dict[str, Any]:
    """Get component styles from the theme."""
    return COMPONENT_STYLES.get(component_name, {})
