"""
Onboarding flow components for job application automation system.
Provides a beautiful 5-step setup process with modern UI.
"""

import streamlit as st
import streamlit_antd_components as sac
import streamlit_shadcn_ui as ui
from typing import Dict, Any, Optional
from pathlib import Path

from datetime import datetime
from models import UserProfile, ExperienceLevel
from database.manager import db_manager
from utils.config import config_manager
from utils.logger import get_logger

logger = get_logger(__name__)


class OnboardingFlow:
    """Manages the 5-step onboarding process."""
    
    def __init__(self):
        self.steps = [
            {"title": "Welcome", "icon": "🚀", "description": "Privacy & CV Upload"},
            {"title": "Credentials", "icon": "🔐", "description": "AI API Setup"},
            {"title": "Complete", "icon": "✅", "description": "Ready to Go!"}
        ]
    
    def render(self) -> bool:
        """Render the onboarding flow. Returns True if completed."""
        # Initialize session state
        if 'onboarding_step' not in st.session_state:
            st.session_state.onboarding_step = 0
        if 'onboarding_data' not in st.session_state:
            st.session_state.onboarding_data = {}
        
        # Render progress indicator
        self._render_progress()
        
        # Render current step
        current_step = st.session_state.onboarding_step

        if current_step == 0:
            return self._render_welcome_and_cv_upload()
        elif current_step == 1:
            return self._render_credentials_setup()
        elif current_step == 2:
            return self._render_completion()

        return False
    
    def _render_progress(self):
        """Render the progress indicator."""
        current_step = st.session_state.onboarding_step
        
        # Create progress steps
        steps_data = []
        for i, step in enumerate(self.steps):
            status = "finish" if i < current_step else ("process" if i == current_step else "wait")
            steps_data.append({
                "title": step["title"],
                "description": step["description"],
                "status": status
            })
        
        # Render with antd components
        sac.steps(
            items=steps_data,
            index=current_step,
            format_func='title',
            size='default'
        )
        
        st.markdown("---")
    
    def _render_welcome_and_cv_upload(self) -> bool:
        """Render welcome step with CV upload."""
        st.markdown("""
        <div style="text-align: center; padding: 2rem;">
            <h1>🚀 Welcome to JobFlow</h1>
            <h3>Smart CV-Powered Job Application Automation</h3>
            <p style="font-size: 1.2em; color: #666;">
                Upload your CV and we'll handle the rest - just 3 simple steps!
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Privacy-first messaging
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            ui.card(
                content="""
                ### 🔒 Privacy First & AI-Powered

                - **Local Storage**: All your data stays on your device
                - **Smart CV Optimization**: AI tweaks your CV for each job
                - **No Manual Entry**: Extract profile info from your CV automatically
                - **Encrypted Credentials**: API keys are encrypted locally

                ### ⚡ Quick Setup Process

                1. **Upload CV** - We extract your profile automatically
                2. **Add AI Credentials** - For smart CV optimization
                3. **Start Applying** - Automated job search and applications
                """,
                key="privacy_card"
            )

        st.markdown("---")

        # CV Upload Section
        st.subheader("📄 Upload Your CV")
        st.write("Upload your CV and we'll automatically extract your profile information.")

        uploaded_file = st.file_uploader(
            "Choose your CV file",
            type=['pdf', 'docx', 'doc'],
            help="Supported formats: PDF, DOCX, DOC",
            key="cv_upload"
        )

        if uploaded_file is not None:
            # Save uploaded file
            from utils.config import config_manager
            cv_dir = config_manager.get_data_dir() / "cvs"
            cv_dir.mkdir(exist_ok=True)
            cv_path = cv_dir / uploaded_file.name

            with open(cv_path, 'wb') as f:
                f.write(uploaded_file.getbuffer())

            # Parse CV
            with st.spinner("🔍 Analyzing your CV..."):
                try:
                    from utils.cv_parser import cv_parser
                    cv_data = cv_parser.parse_cv(str(cv_path))

                    # Store CV data in session state
                    st.session_state.onboarding_data = cv_data
                    st.session_state.onboarding_data['cv_path'] = str(cv_path)

                    # Show extracted information
                    st.success("✅ CV analyzed successfully!")

                    col1, col2 = st.columns(2)
                    with col1:
                        ui.card(
                            content=f"""
                            ### 👤 Extracted Profile

                            **Name**: {cv_data.get('name', 'Not found')}
                            **Email**: {cv_data.get('email', 'Not found')}
                            **Experience**: {cv_data.get('experience_level', 'mid')} ({cv_data.get('years_of_experience', 0)} years)
                            **Skills Found**: {len(cv_data.get('skills', []))} skills
                            """,
                            key="extracted_profile"
                        )

                    with col2:
                        ui.card(
                            content=f"""
                            ### 💼 Job Preferences

                            **Job Types**: {', '.join(cv_data.get('preferred_job_types', ['Software Developer'])[:3])}
                            **Top Skills**: {', '.join(cv_data.get('skills', [])[:5])}
                            **Location**: Remote (default)
                            """,
                            key="job_preferences"
                        )

                    # Navigation button
                    st.markdown("<br>", unsafe_allow_html=True)
                    col1, col2, col3 = st.columns([1, 1, 1])
                    with col2:
                        if st.button("✨ Continue to AI Setup", use_container_width=True, type="primary"):
                            st.session_state.onboarding_step = 1
                            st.rerun()

                except Exception as e:
                    st.error(f"❌ Failed to parse CV: {e}")
                    st.info("💡 Please ensure your CV is a valid PDF or DOCX file with readable text.")

        else:
            # Show sample CV info
            st.info("📝 Upload your CV to automatically extract your profile information and get started!")

        return False
    
    def _render_credentials_setup(self) -> bool:
        """Render simplified credentials setup step."""
        st.header("🤖 AI Credentials Setup")
        st.write("Add your AI API credentials to enable smart CV optimization and cover letter generation.")

        # Show why we need credentials
        ui.card(
            content="""
            ### 🧠 Why AI Credentials?

            - **Smart CV Optimization**: Automatically tweak your CV for each job
            - **Custom Cover Letters**: Generate personalized cover letters
            - **Job Matching**: Analyze how well you match job requirements
            - **Privacy**: All processing happens through your own API keys

            **Recommended**: OpenAI (GPT-4) or Anthropic (Claude) for best results
            """,
            key="ai_benefits_card"
        )

        st.markdown("---")

        # Simplified credential form - just the essentials
        st.subheader("🔑 Add Your AI API Key")

        # Provider selection
        provider = st.selectbox(
            "Choose AI Provider",
            options=["openai", "anthropic"],
            format_func=lambda x: "OpenAI (GPT-4)" if x == "openai" else "Anthropic (Claude)",
            help="Both providers offer excellent CV optimization capabilities"
        )

        # API key input
        api_key = st.text_input(
            f"{'OpenAI' if provider == 'openai' else 'Anthropic'} API Key",
            type="password",
            placeholder="sk-..." if provider == "openai" else "sk-ant-...",
            help=f"Get your API key from {'https://platform.openai.com/api-keys' if provider == 'openai' else 'https://console.anthropic.com/'}"
        )

        # Validation and save
        col1, col2, col3 = st.columns([1, 1, 1])

        with col1:
            if st.button("← Back", use_container_width=True):
                st.session_state.onboarding_step = 0
                st.rerun()

        with col2:
            if st.button("🔍 Test API Key", use_container_width=True, disabled=not api_key):
                if api_key:
                    with st.spinner("Testing API connection..."):
                        # Test the API key
                        test_result = self._test_api_key(provider, api_key)
                        if test_result:
                            st.success("✅ API key is valid!")
                            # Store the credential
                            user_email = st.session_state.onboarding_data.get('email')
                            if user_email:
                                db_manager.save_credential(
                                    user_email=user_email,
                                    service_name=provider,
                                    credential_type="api_key",
                                    credential_data={"api_key": api_key}
                                )
                                st.session_state.credentials_saved = True
                        else:
                            st.error("❌ API key is invalid or expired")

        with col3:
            # Allow skipping credentials for now
            if st.button("Skip for Now →", use_container_width=True):
                st.session_state.onboarding_step = 2
                st.rerun()

        # Continue button (only show if credentials are saved or user wants to skip)
        if st.session_state.get('credentials_saved', False):
            st.markdown("<br>", unsafe_allow_html=True)
            col1, col2, col3 = st.columns([1, 1, 1])
            with col2:
                if st.button("🚀 Complete Setup", use_container_width=True, type="primary"):
                    st.session_state.onboarding_step = 2
                    st.rerun()

        # Show helpful links
        st.markdown("---")
        st.markdown("### 📚 Getting API Keys")

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("""
            **OpenAI API Key:**
            1. Go to [platform.openai.com](https://platform.openai.com/api-keys)
            2. Sign up or log in
            3. Create a new API key
            4. Copy and paste it above
            """)

        with col2:
            st.markdown("""
            **Anthropic API Key:**
            1. Go to [console.anthropic.com](https://console.anthropic.com/)
            2. Sign up or log in
            3. Create a new API key
            4. Copy and paste it above
            """)

        return False

    def _test_api_key(self, provider: str, api_key: str) -> bool:
        """Test if the API key is valid."""
        try:
            if provider == "openai":
                import openai
                client = openai.OpenAI(api_key=api_key)
                # Test with a simple request
                response = client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": "Hello"}],
                    max_tokens=5
                )
                return True
            elif provider == "anthropic":
                import anthropic
                client = anthropic.Anthropic(api_key=api_key)
                # Test with a simple request
                response = client.messages.create(
                    model="claude-3-haiku-20240307",
                    max_tokens=5,
                    messages=[{"role": "user", "content": "Hello"}]
                )
                return True
        except Exception as e:
            logger.error(f"API key test failed: {e}")
            return False

        return False
    
    def _render_credentials_setup(self) -> bool:
        """Render credentials setup step."""
        st.header("🔐 API Credentials Setup")
        st.write("Configure your API keys and credentials for enhanced job search capabilities.")
        
        # Import credential manager here to avoid circular imports
        from .credentials import CredentialManager
        credential_manager = CredentialManager()
        
        # Render credential setup
        credential_manager.render_setup_form()
        
        # Navigation
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            if st.button("← Back", use_container_width=True):
                st.session_state.onboarding_step = 1
                st.rerun()
        
        with col3:
            if st.button("Next →", use_container_width=True, type="primary"):
                st.session_state.onboarding_step = 3
                st.rerun()
        
        return False
    
    def _render_completion(self) -> bool:
        """Render completion step."""
        st.header("🎉 Welcome to JobFlow!")
        st.write("Your AI-powered job application system is ready to go!")

        # Save user profile to database
        try:
            profile_data = st.session_state.onboarding_data

            # Create user profile from CV data
            from utils.cv_parser import cv_parser
            user_profile = cv_parser.create_user_profile(profile_data)

            # Save to database
            db_manager.save_user_profile(user_profile)

            # Store user email for session
            st.session_state.user_email = user_profile.email
            st.session_state.user_profile = user_profile

            # Mark onboarding as complete
            st.session_state.onboarding_complete = True

            # Save completion flag and user email
            completion_file = config_manager.get_data_dir() / "onboarding_complete.json"
            user_file = config_manager.get_data_dir() / "current_user.json"

            import json
            with open(completion_file, 'w') as f:
                json.dump({"completed": True, "date": datetime.now().isoformat()}, f)

            with open(user_file, 'w') as f:
                json.dump({"email": user_profile.email}, f)

            st.success("✅ Profile created successfully!")

        except Exception as e:
            st.error(f"Error saving profile: {e}")
            logger.error(f"Failed to save profile during onboarding: {e}")
            return False

        # Show success summary
        col1, col2 = st.columns(2)
        with col1:
            ui.card(
                content=f"""
                ### 👤 Your Profile

                **Name**: {st.session_state.onboarding_data.get('name', 'Unknown')}
                **Email**: {st.session_state.onboarding_data.get('email', 'Not provided')}
                **Experience**: {st.session_state.onboarding_data.get('experience_level', 'mid')} level
                **Skills**: {len(st.session_state.onboarding_data.get('skills', []))} skills detected
                **CV**: Uploaded and analyzed ✅
                """,
                key="profile_summary"
            )

        with col2:
            ui.card(
                content=f"""
                ### 🤖 AI Features

                **CV Optimization**: {'✅ Ready' if st.session_state.get('credentials_saved') else '⚠️ Add API key for full features'}
                **Cover Letters**: {'✅ Ready' if st.session_state.get('credentials_saved') else '⚠️ Add API key for generation'}
                **Job Matching**: {'✅ Ready' if st.session_state.get('credentials_saved') else '⚠️ Add API key for analysis'}
                **Privacy**: All data stored locally 🔒
                """,
                key="ai_features"
            )

        # Quick start guide
        st.markdown("---")
        st.subheader("🚀 Quick Start Guide")

        col1, col2, col3 = st.columns(3)

        with col1:
            ui.card(
                content="""
                ### 1. Search Jobs

                - Use the **Job Search** tab
                - Enter keywords and location
                - Select job portals to search
                - Review and save interesting jobs
                """,
                key="step1_card"
            )

        with col2:
            ui.card(
                content="""
                ### 2. Optimize & Apply

                - AI optimizes your CV for each job
                - Generate custom cover letters
                - Review before applying
                - Track application status
                """,
                key="step2_card"
            )

        with col3:
            ui.card(
                content="""
                ### 3. Track Progress

                - Monitor applications in dashboard
                - View success metrics
                - Export your data anytime
                - Improve your strategy
                """,
                key="step3_card"
            )

        st.markdown("<br>", unsafe_allow_html=True)

        # Launch button
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button("🚀 Start Job Hunting!", use_container_width=True, type="primary"):
                # Clear onboarding data but keep user info
                if 'onboarding_data' in st.session_state:
                    del st.session_state.onboarding_data
                if 'onboarding_step' in st.session_state:
                    del st.session_state.onboarding_step

                # Set default page to Job Search for immediate action
                st.session_state.current_page = "Job Search"
                st.rerun()

        # Add credentials later option
        if not st.session_state.get('credentials_saved'):
            st.markdown("<br>", unsafe_allow_html=True)
            st.info("💡 **Tip**: Add AI credentials later in the Credentials tab to unlock CV optimization and cover letter generation!")

        return True
