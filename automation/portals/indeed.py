"""
Indeed job portal automation.
Handles job searching and application submission on Indeed.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import urllib.parse

from automation.browser import JobPortalAutomation
from models import JobPortalName
from utils.logger import get_logger

logger = get_logger(__name__)

class IndeedAutomation(JobPortalAutomation):
    """Indeed-specific automation implementation."""
    
    def __init__(self):
        super().__init__(JobPortalName.INDEED)
        self.base_url = "https://www.indeed.com"
    
    async def _perform_login(self, email: str, password: str) -> bool:
        """Perform Indeed login."""
        try:
            # Navigate to login page
            await self.page.goto(f"{self.base_url}/account/login")
            await self.page.wait_for_load_state('networkidle')
            
            # Handle cookie consent if present
            await self._handle_cookie_consent()
            
            # Fill login form
            email_selector = '#ifl-InputFormField-3'  # Indeed's email field ID
            password_selector = '#ifl-InputFormField-4'  # Indeed's password field ID
            login_button = 'button[type="submit"]'
            
            # Try alternative selectors if primary ones don't work
            if not await self.wait_for_element(email_selector, timeout=3000):
                email_selector = 'input[name="__email"]'
                password_selector = 'input[name="__password"]'
            
            if not await self.wait_for_element(email_selector):
                logger.error("Indeed email field not found")
                return False
            
            await self.safe_fill(email_selector, email)
            await self.safe_fill(password_selector, password)
            
            # Click login button
            await self.safe_click(login_button)
            await self.page.wait_for_timeout(3000)
            
            # Check if login was successful
            current_url = self.page.url
            if 'account/login' not in current_url and 'challenge' not in current_url:
                logger.info("Indeed login successful")
                return True
            
            # Check for 2FA or security challenges
            if await self.page.query_selector('[data-testid="challenge"]'):
                logger.warning("Indeed security challenge detected - manual intervention required")
                return False
            
            logger.error("Indeed login failed")
            return False
            
        except Exception as e:
            logger.error(f"Indeed login error: {str(e)}")
            return False
    
    async def _handle_cookie_consent(self):
        """Handle cookie consent popup."""
        try:
            accept_button = 'button[id*="onetrust-accept"], button:has-text("Accept")'
            if await self.wait_for_element(accept_button, timeout=3000):
                await self.safe_click(accept_button)
                await self.page.wait_for_timeout(1000)
        except Exception as e:
            logger.debug(f"No cookie consent found: {str(e)}")
    
    async def _perform_job_search(self, keywords: List[str], location: str = "") -> List[Dict[str, Any]]:
        """Perform Indeed job search."""
        try:
            jobs = []
            search_query = " ".join(keywords)
            
            # Build search URL
            params = {
                'q': search_query,
                'l': location or 'Remote',
                'fromage': '7',  # Last 7 days
                'sort': 'date',  # Sort by date
                'radius': '25'  # 25 mile radius
            }
            
            search_url = f"{self.base_url}/jobs?" + urllib.parse.urlencode(params)
            
            # Navigate to search results
            await self.page.goto(search_url)
            await self.page.wait_for_load_state('networkidle')
            
            # Handle cookie consent
            await self._handle_cookie_consent()
            
            # Apply additional filters
            await self._apply_search_filters()
            
            # Extract job listings
            jobs = await self._extract_job_listings()
            
            logger.info(f"Found {len(jobs)} jobs on Indeed")
            return jobs
            
        except Exception as e:
            logger.error(f"Indeed job search error: {str(e)}")
            return []
    
    async def _apply_search_filters(self):
        """Apply search filters for better job matching."""
        try:
            # Filter for remote jobs
            remote_filter = 'a[aria-label*="remote"], a:has-text("Remote")'
            if await self.wait_for_element(remote_filter, timeout=3000):
                await self.safe_click(remote_filter)
                await self.page.wait_for_load_state('networkidle')
            
            # Filter for jobs posted in last 7 days
            date_filter = 'a[aria-label*="last 7 days"], a:has-text("Last 7 days")'
            if await self.wait_for_element(date_filter, timeout=3000):
                await self.safe_click(date_filter)
                await self.page.wait_for_load_state('networkidle')
                
        except Exception as e:
            logger.warning(f"Could not apply Indeed filters: {str(e)}")
    
    async def _extract_job_listings(self) -> List[Dict[str, Any]]:
        """Extract job listings from search results."""
        jobs = []
        
        try:
            # Wait for job listings to load
            job_cards_selector = '[data-testid="job-tile"], .job_seen_beacon'
            await self.page.wait_for_selector(job_cards_selector, timeout=10000)
            
            # Get all job cards
            job_cards = await self.page.query_selector_all(job_cards_selector)
            
            for i, card in enumerate(job_cards[:20]):  # Limit to first 20 jobs
                try:
                    job_data = await self._extract_job_data(card)
                    if job_data:
                        jobs.append(job_data)
                except Exception as e:
                    logger.warning(f"Error extracting Indeed job {i}: {str(e)}")
                    continue
            
        except Exception as e:
            logger.error(f"Error extracting Indeed job listings: {str(e)}")
        
        return jobs
    
    async def _extract_job_data(self, card) -> Optional[Dict[str, Any]]:
        """Extract data from a single job card."""
        try:
            # Extract job title
            title_element = await card.query_selector('h2 a span, .jobTitle a span')
            title = await title_element.inner_text() if title_element else "Unknown Title"
            
            # Extract company name
            company_element = await card.query_selector('[data-testid="company-name"], .companyName')
            company = await company_element.inner_text() if company_element else "Unknown Company"
            
            # Extract location
            location_element = await card.query_selector('[data-testid="job-location"], .companyLocation')
            location = await location_element.inner_text() if location_element else "Unknown Location"
            
            # Extract job URL
            link_element = await card.query_selector('h2 a, .jobTitle a')
            job_url = await link_element.get_attribute('href') if link_element else ""
            if job_url and not job_url.startswith('http'):
                job_url = f"{self.base_url}{job_url}"
            
            # Extract salary if available
            salary_element = await card.query_selector('[data-testid="salary-snippet"], .salary-snippet')
            salary = await salary_element.inner_text() if salary_element else ""
            
            # Extract job snippet/description
            snippet_element = await card.query_selector('[data-testid="job-snippet"], .summary')
            snippet = await snippet_element.inner_text() if snippet_element else ""
            
            # Extract posted date
            posted_element = await card.query_selector('[data-testid="myJobsStateDate"], .date')
            posted_date = await posted_element.inner_text() if posted_element else "Unknown"
            
            # Check if it's an Easy Apply job
            easy_apply = await card.query_selector('[data-testid="indeedApply"], .indeedApply') is not None
            
            return {
                'title': title.strip(),
                'company': company.strip(),
                'location': location.strip(),
                'url': job_url,
                'posted_date': posted_date.strip(),
                'portal': 'Indeed',
                'easy_apply': easy_apply,
                'description': snippet.strip(),
                'salary': salary.strip(),
                'job_type': 'Full-time',  # Default assumption
                'extracted_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error extracting Indeed job data: {str(e)}")
            return None
    
    async def _perform_job_application(self, job_data: Dict[str, Any], 
                                     resume_path: str, cover_letter: str) -> bool:
        """Perform Indeed job application."""
        try:
            job_url = job_data.get('url', '')
            if not job_url:
                logger.error("No job URL provided for Indeed application")
                return False
            
            # Navigate to job page
            await self.page.goto(job_url)
            await self.page.wait_for_load_state('networkidle')
            
            # Handle cookie consent
            await self._handle_cookie_consent()
            
            # Check if Indeed Apply is available
            apply_button = '[data-testid="indeedApply"], .indeedApply, button:has-text("Apply now")'
            if not await self.wait_for_element(apply_button, timeout=5000):
                logger.warning("Indeed Apply button not found - job may require external application")
                return False
            
            # Click Apply button
            await self.safe_click(apply_button)
            await self.page.wait_for_timeout(2000)
            
            # Handle the application flow
            success = await self._handle_application_flow(resume_path, cover_letter)
            
            if success:
                logger.info(f"Successfully applied to {job_data.get('title', 'Unknown')} at {job_data.get('company', 'Unknown')}")
            
            return success
            
        except Exception as e:
            logger.error(f"Indeed job application error: {str(e)}")
            return False
    
    async def _handle_application_flow(self, resume_path: str, cover_letter: str) -> bool:
        """Handle the Indeed application flow."""
        try:
            max_steps = 5
            current_step = 0
            
            while current_step < max_steps:
                current_step += 1
                
                # Check if we're on an application form
                if await self.page.query_selector('form[data-testid="apply-form"], .ia-BasePage-content'):
                    
                    # Handle resume upload
                    file_input = 'input[type="file"]'
                    if await self.wait_for_element(file_input, timeout=2000):
                        await self.upload_file(file_input, resume_path)
                        await self.page.wait_for_timeout(3000)
                    
                    # Handle cover letter
                    cover_letter_textarea = 'textarea[name*="cover"], textarea[id*="cover"]'
                    if await self.wait_for_element(cover_letter_textarea, timeout=2000):
                        await self.safe_fill(cover_letter_textarea, cover_letter)
                    
                    # Handle additional questions
                    await self._handle_application_questions()
                    
                    # Look for Continue/Next button
                    continue_button = 'button:has-text("Continue"), button:has-text("Next"), button[data-testid="continue"]'
                    if await self.wait_for_element(continue_button, timeout=3000):
                        await self.safe_click(continue_button)
                        await self.page.wait_for_timeout(2000)
                        continue
                    
                    # Look for Submit button
                    submit_button = 'button:has-text("Submit"), button:has-text("Apply"), button[data-testid="submit"]'
                    if await self.wait_for_element(submit_button, timeout=3000):
                        await self.safe_click(submit_button)
                        await self.page.wait_for_timeout(3000)
                        
                        # Check for success confirmation
                        success_indicator = '[data-testid="application-complete"], .ia-ApplicationComplete'
                        if await self.wait_for_element(success_indicator, timeout=5000):
                            logger.info("Indeed application submission successful")
                            return True
                        
                        break
                
                # If no form elements found, we might be done or stuck
                break
            
            logger.warning("Indeed application flow completed but success not confirmed")
            return False
            
        except Exception as e:
            logger.error(f"Indeed application flow error: {str(e)}")
            return False
    
    async def _handle_application_questions(self):
        """Handle additional application questions."""
        try:
            # Handle yes/no questions (common on Indeed)
            yes_buttons = await self.page.query_selector_all('button:has-text("Yes"), input[value="Yes"]')
            for button in yes_buttons[:3]:  # Limit to first 3 to avoid over-clicking
                try:
                    await button.click()
                    await self.page.wait_for_timeout(500)
                except:
                    continue
            
            # Handle text inputs for years of experience
            text_inputs = await self.page.query_selector_all('input[type="text"]:not([disabled])')
            for input_field in text_inputs:
                try:
                    placeholder = await input_field.get_attribute('placeholder')
                    if placeholder and ('year' in placeholder.lower() or 'experience' in placeholder.lower()):
                        await input_field.fill('3')  # Default years of experience
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"Error handling Indeed application questions: {str(e)}")
    
    async def get_job_description(self, job_url: str) -> str:
        """Get full job description from job page."""
        try:
            await self.page.goto(job_url)
            await self.page.wait_for_load_state('networkidle')
            
            # Handle cookie consent
            await self._handle_cookie_consent()
            
            # Extract job description
            description_selector = '[data-testid="jobsearch-JobComponent-description"], .jobsearch-jobDescriptionText'
            if await self.wait_for_element(description_selector):
                description_element = await self.page.query_selector(description_selector)
                description = await description_element.inner_text()
                return description.strip()
            
            return ""
            
        except Exception as e:
            logger.error(f"Error getting Indeed job description: {str(e)}")
            return ""
