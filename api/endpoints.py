"""
API Endpoints for JobFlow
Provides REST API endpoints for external communication and integrations.
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any
from datetime import datetime
import asyncio
import uvicorn

from models import JobPortalName, ApplicationStatus
from database.manager import db_manager
from automation.queue_manager import queue_manager, QueueJobType
from ai.openrouter_client import openrouter_client
from ai.cost_tracker import CostTracker
from utils.config import config_manager
from utils.logger import get_logger

logger = get_logger(__name__)

# FastAPI app
app = FastAPI(
    title="JobFlow API",
    description="API for JobFlow - Smart Job Application Automation",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Request/Response Models
class JobSearchRequest(BaseModel):
    """Request model for job search."""
    keywords: List[str]
    location: str = ""
    portals: List[str] = Field(default_factory=lambda: ["we_work_remotely"])
    max_results: int = Field(default=20, ge=1, le=100)

class JobSearchResponse(BaseModel):
    """Response model for job search."""
    total_jobs: int
    jobs_by_portal: Dict[str, int]
    jobs: List[Dict[str, Any]]
    search_id: str
    timestamp: datetime

class CVAnalysisRequest(BaseModel):
    """Request model for CV analysis."""
    cv_text: str
    user_context: Optional[Dict[str, Any]] = None

class CVAnalysisResponse(BaseModel):
    """Response model for CV analysis."""
    analysis_id: str
    skills: List[str]
    experience_level: str
    years_experience: int
    strengths: List[str]
    recommended_job_types: List[str]
    confidence_score: float
    cost_usd: float
    processing_time: float

class QueueJobRequest(BaseModel):
    """Request model for queue jobs."""
    job_type: str
    parameters: Dict[str, Any]
    priority: int = Field(default=5, ge=1, le=10)
    auto_retry: bool = False

class QueueJobResponse(BaseModel):
    """Response model for queue jobs."""
    job_id: str
    status: str
    progress: float
    estimated_duration: Optional[int]
    estimated_cost: Optional[float]
    created_at: datetime

class HealthCheckResponse(BaseModel):
    """Response model for health check."""
    status: str
    timestamp: datetime
    version: str
    database_connected: bool
    queue_active: bool
    ai_service_available: bool

class CostSummaryResponse(BaseModel):
    """Response model for cost summary."""
    daily_cost: float
    monthly_cost: float
    total_requests: int
    cost_by_model: Dict[str, float]
    cost_by_type: Dict[str, float]

# Authentication
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user from token."""
    # For now, just return a placeholder user
    # In production, implement proper JWT token validation
    token = credentials.credentials
    if not token:
        raise HTTPException(status_code=401, detail="Invalid authentication credentials")
    
    # Placeholder user extraction from token
    user_email = "<EMAIL>"  # Extract from JWT token
    return user_email

# Health Check Endpoints
@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint."""
    try:
        # Check database connection
        db_connected = True
        try:
            # Test database connection
            db_manager.get_application_stats("<EMAIL>", days=1)
        except Exception:
            db_connected = False
        
        # Check queue status
        queue_stats = queue_manager.get_queue_stats()
        queue_active = queue_stats['workers_active'] > 0
        
        # Check AI service
        ai_available = bool(config_manager.get_setting("OPENROUTER_API_KEY"))
        
        return HealthCheckResponse(
            status="healthy" if all([db_connected, queue_active, ai_available]) else "degraded",
            timestamp=datetime.now(),
            version="1.0.0",
            database_connected=db_connected,
            queue_active=queue_active,
            ai_service_available=ai_available
        )
    except Exception as e:
        logger.error(f"Health check error: {str(e)}")
        raise HTTPException(status_code=500, detail="Health check failed")

@app.get("/status")
async def get_system_status():
    """Get detailed system status."""
    try:
        queue_stats = queue_manager.get_queue_stats()
        cost_tracker = CostTracker()
        daily_costs = await cost_tracker.get_daily_summary()
        
        return {
            "timestamp": datetime.now(),
            "queue": queue_stats,
            "costs": {
                "daily_total": daily_costs.get('total_cost', 0),
                "daily_requests": daily_costs.get('total_requests', 0),
                "daily_tokens": daily_costs.get('total_tokens', 0)
            },
            "services": {
                "database": "connected",
                "queue": "active" if queue_stats['workers_active'] > 0 else "inactive",
                "ai": "available" if config_manager.get_setting("OPENROUTER_API_KEY") else "unavailable"
            }
        }
    except Exception as e:
        logger.error(f"Status check error: {str(e)}")
        raise HTTPException(status_code=500, detail="Status check failed")

# Job Search Endpoints
@app.post("/api/v1/jobs/search", response_model=JobSearchResponse)
async def search_jobs(request: JobSearchRequest, user_email: str = Depends(get_current_user)):
    """Search for jobs across portals."""
    try:
        # Convert portal names to enums
        portals = []
        for portal_name in request.portals:
            try:
                portals.append(JobPortalName(portal_name))
            except ValueError:
                logger.warning(f"Unknown portal: {portal_name}")
        
        if not portals:
            raise HTTPException(status_code=400, detail="No valid portals specified")
        
        # Perform search
        from automation.api_client import api_manager
        results = await api_manager.search_all_portals(
            keywords=request.keywords,
            location=request.location,
            enabled_portals=portals
        )
        
        # Format response
        all_jobs = []
        jobs_by_portal = {}
        
        for portal, jobs in results.items():
            jobs_by_portal[portal.value] = len(jobs)
            all_jobs.extend(jobs[:request.max_results])
        
        search_id = f"search_{user_email}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        return JobSearchResponse(
            total_jobs=len(all_jobs),
            jobs_by_portal=jobs_by_portal,
            jobs=all_jobs,
            search_id=search_id,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Job search error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Job search failed: {str(e)}")

@app.get("/api/v1/jobs/portals")
async def get_supported_portals():
    """Get list of supported job portals."""
    return {
        "portals": [
            {
                "name": portal.value,
                "display_name": portal.value.replace("_", " ").title(),
                "supported": True
            }
            for portal in JobPortalName
        ]
    }

# AI Endpoints
@app.post("/api/v1/ai/analyze-cv", response_model=CVAnalysisResponse)
async def analyze_cv(request: CVAnalysisRequest, user_email: str = Depends(get_current_user)):
    """Analyze CV using AI."""
    try:
        if not request.cv_text.strip():
            raise HTTPException(status_code=400, detail="CV text cannot be empty")
        
        # Analyze CV
        analysis = await openrouter_client.analyze_cv(request.cv_text, request.user_context)
        
        # Get cost information
        daily_summary = await openrouter_client.get_daily_cost_summary()
        
        analysis_id = f"cv_analysis_{user_email}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        return CVAnalysisResponse(
            analysis_id=analysis_id,
            skills=analysis.skills,
            experience_level=analysis.experience_level,
            years_experience=analysis.years_experience,
            strengths=analysis.strengths,
            recommended_job_types=analysis.recommended_job_types,
            confidence_score=analysis.confidence_score,
            cost_usd=0.0,  # Would be calculated from the actual API call
            processing_time=0.0  # Would be measured
        )
        
    except Exception as e:
        logger.error(f"CV analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CV analysis failed: {str(e)}")

@app.get("/api/v1/ai/costs", response_model=CostSummaryResponse)
async def get_ai_costs(user_email: str = Depends(get_current_user)):
    """Get AI usage costs."""
    try:
        cost_tracker = CostTracker()
        daily_summary = await cost_tracker.get_daily_summary()
        monthly_summary = await cost_tracker.get_monthly_summary()
        
        return CostSummaryResponse(
            daily_cost=daily_summary.get('total_cost', 0),
            monthly_cost=monthly_summary.get('total_cost', 0),
            total_requests=monthly_summary.get('total_requests', 0),
            cost_by_model=monthly_summary.get('by_model', {}),
            cost_by_type=monthly_summary.get('by_type', {})
        )
        
    except Exception as e:
        logger.error(f"Cost summary error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Cost summary failed: {str(e)}")

# Queue Management Endpoints
@app.post("/api/v1/queue/jobs", response_model=QueueJobResponse)
async def create_queue_job(request: QueueJobRequest, user_email: str = Depends(get_current_user)):
    """Create a new queue job."""
    try:
        # Validate job type
        try:
            job_type = QueueJobType(request.job_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid job type: {request.job_type}")
        
        # Create job
        job_id = await queue_manager.add_job(
            job_type=job_type,
            user_email=user_email,
            parameters=request.parameters,
            priority=request.priority,
            auto_retry=request.auto_retry
        )
        
        # Get job details
        job = queue_manager.get_job_status(job_id)
        
        return QueueJobResponse(
            job_id=job_id,
            status=job.status.value,
            progress=job.progress,
            estimated_duration=job.estimated_duration,
            estimated_cost=job.estimated_cost,
            created_at=job.created_at
        )
        
    except Exception as e:
        logger.error(f"Queue job creation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Job creation failed: {str(e)}")

@app.get("/api/v1/queue/jobs/{job_id}")
async def get_queue_job(job_id: str, user_email: str = Depends(get_current_user)):
    """Get queue job status."""
    try:
        job = queue_manager.get_job_status(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        if job.user_email != user_email:
            raise HTTPException(status_code=403, detail="Access denied")
        
        return {
            "job_id": job.job_id,
            "status": job.status.value,
            "progress": job.progress,
            "current_step": job.current_step,
            "created_at": job.created_at,
            "started_at": job.started_at,
            "completed_at": job.completed_at,
            "result": job.result,
            "error_message": job.error_message,
            "estimated_cost": job.estimated_cost,
            "actual_cost": job.actual_cost
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get queue job error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get job: {str(e)}")

@app.get("/api/v1/queue/jobs")
async def get_user_queue_jobs(user_email: str = Depends(get_current_user), limit: int = 50):
    """Get user's queue jobs."""
    try:
        jobs = queue_manager.get_user_jobs(user_email, limit)
        
        return {
            "jobs": [
                {
                    "job_id": job.job_id,
                    "job_type": job.job_type.value,
                    "status": job.status.value,
                    "progress": job.progress,
                    "created_at": job.created_at,
                    "estimated_cost": job.estimated_cost,
                    "actual_cost": job.actual_cost
                }
                for job in jobs
            ],
            "total": len(jobs)
        }
        
    except Exception as e:
        logger.error(f"Get user jobs error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get jobs: {str(e)}")

@app.get("/api/v1/queue/stats")
async def get_queue_stats():
    """Get queue statistics."""
    try:
        return queue_manager.get_queue_stats()
    except Exception as e:
        logger.error(f"Queue stats error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get queue stats: {str(e)}")

# Connection Testing Endpoints
@app.post("/api/v1/test/portal/{portal_name}")
async def test_portal_connection(portal_name: str):
    """Test connection to a job portal."""
    try:
        # Validate portal name
        try:
            portal = JobPortalName(portal_name)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid portal: {portal_name}")
        
        # Test connection
        from automation.api_client import api_manager
        client = api_manager.get_client(portal)
        
        if not client:
            raise HTTPException(status_code=404, detail=f"Client not available for portal: {portal_name}")
        
        # Perform a simple test (e.g., fetch a few jobs)
        test_results = await client.search_jobs(["test"], limit=1)
        
        return {
            "portal": portal_name,
            "status": "connected",
            "test_results": len(test_results) if test_results else 0,
            "timestamp": datetime.now()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Portal connection test error: {str(e)}")
        return {
            "portal": portal_name,
            "status": "failed",
            "error": str(e),
            "timestamp": datetime.now()
        }

@app.post("/api/v1/test/ai")
async def test_ai_connection():
    """Test AI service connection."""
    try:
        # Test with a simple request
        test_response = await openrouter_client._make_request({
            "model": "anthropic/claude-3.5-sonnet",
            "messages": [{"role": "user", "content": "Hello, this is a test."}],
            "max_tokens": 10
        })
        
        return {
            "service": "ai",
            "status": "connected",
            "model": test_response.model,
            "cost": test_response.cost_usd,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"AI connection test error: {str(e)}")
        return {
            "service": "ai",
            "status": "failed",
            "error": str(e),
            "timestamp": datetime.now()
        }

# Run the API server
def run_api_server(host: str = "0.0.0.0", port: int = 8000):
    """Run the API server."""
    uvicorn.run(app, host=host, port=port)

if __name__ == "__main__":
    run_api_server()
