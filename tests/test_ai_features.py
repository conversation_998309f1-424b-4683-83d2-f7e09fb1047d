"""
Unit tests for AI features.
Tests CV processing, cost tracking, and job matching functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
import tempfile
import json

from ai.openrouter_client import OpenRouterClient, CVAnalysis, JobMatchScore
from ai.cost_tracker import <PERSON>stT<PERSON>, APIUsageRecord
from ai.cv_processor import CVProcessor, ProcessingJob

class TestOpenRouterClient:
    """Test cases for OpenRouter API client."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return OpenRouterClient(api_key="test_key")
    
    @pytest.fixture
    def mock_cv_analysis(self):
        """Mock CV analysis response."""
        return CVAnalysis(
            skills=["Python", "JavaScript", "React"],
            experience_level="mid",
            years_experience=3,
            job_titles=["Software Developer", "Frontend Developer"],
            industries=["Technology", "Software"],
            strengths=["Problem solving", "Team collaboration"],
            improvement_areas=["Leadership skills"],
            recommended_job_types=["Full-stack Developer", "Software Engineer"],
            confidence_score=0.85
        )
    
    @pytest.mark.asyncio
    async def test_analyze_cv_success(self, client, mock_cv_analysis):
        """Test successful CV analysis."""
        with patch.object(client, '_make_request') as mock_request:
            # Mock the API response
            mock_response = Mock()
            mock_response.content = json.dumps(mock_cv_analysis.model_dump())
            mock_response.model = "anthropic/claude-3.5-sonnet"
            mock_response.tokens_used = 1500
            mock_response.cost_usd = 0.045
            mock_response.processing_time = 2.5
            
            mock_request.return_value = mock_response
            
            # Test CV analysis
            cv_text = "Experienced software developer with 3 years of Python and JavaScript experience..."
            result = await client.analyze_cv(cv_text)
            
            # Assertions
            assert isinstance(result, CVAnalysis)
            assert result.skills == ["Python", "JavaScript", "React"]
            assert result.experience_level == "mid"
            assert result.years_experience == 3
            assert result.confidence_score == 0.85
            
            # Verify API call was made
            mock_request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_cv_invalid_json(self, client):
        """Test CV analysis with invalid JSON response."""
        with patch.object(client, '_make_request') as mock_request:
            # Mock invalid JSON response
            mock_response = Mock()
            mock_response.content = "Invalid JSON response"
            mock_request.return_value = mock_response
            
            cv_text = "Test CV content"
            result = await client.analyze_cv(cv_text)
            
            # Should return a basic analysis with error indicators
            assert isinstance(result, CVAnalysis)
            assert result.skills == ["Analysis failed"]
            assert result.confidence_score == 0.0
    
    @pytest.mark.asyncio
    async def test_match_job_success(self, client, mock_cv_analysis):
        """Test successful job matching."""
        with patch.object(client, '_make_request') as mock_request:
            # Mock job match response
            match_data = {
                "job_id": "test_job_123",
                "match_score": 0.82,
                "matching_skills": ["Python", "JavaScript"],
                "missing_skills": ["Docker", "Kubernetes"],
                "experience_match": True,
                "salary_match": True,
                "location_match": True,
                "overall_recommendation": "Strong match for this position",
                "cover_letter_suggestions": ["Highlight Python experience", "Mention team collaboration"]
            }
            
            mock_response = Mock()
            mock_response.content = json.dumps(match_data)
            mock_request.return_value = mock_response
            
            job_description = "Looking for a Python developer with 3+ years experience..."
            result = await client.match_job(job_description, mock_cv_analysis)
            
            # Assertions
            assert isinstance(result, JobMatchScore)
            assert result.match_score == 0.82
            assert result.matching_skills == ["Python", "JavaScript"]
            assert result.missing_skills == ["Docker", "Kubernetes"]
            assert result.experience_match is True
    
    def test_calculate_cost(self, client):
        """Test cost calculation."""
        usage = {
            "prompt_tokens": 1000,
            "completion_tokens": 500,
            "total_tokens": 1500
        }
        
        cost = client._calculate_cost("anthropic/claude-3.5-sonnet", usage)
        
        # Expected cost: (1000/1000 * 0.003) + (500/1000 * 0.015) = 0.003 + 0.0075 = 0.0105
        assert abs(cost - 0.0105) < 0.0001

class TestCostTracker:
    """Test cases for cost tracking functionality."""
    
    @pytest.fixture
    def cost_tracker(self):
        """Create a test cost tracker."""
        return CostTracker()
    
    @pytest.mark.asyncio
    async def test_track_usage(self, cost_tracker):
        """Test usage tracking."""
        with patch.object(cost_tracker, '_store_usage_record') as mock_store:
            await cost_tracker.track_usage(
                model="anthropic/claude-3.5-sonnet",
                tokens_used=1500,
                cost_usd=0.045,
                request_type="cv_analysis",
                user_email="<EMAIL>"
            )
            
            # Verify record was stored
            mock_store.assert_called_once()
            
            # Check the record structure
            call_args = mock_store.call_args[0][0]
            assert isinstance(call_args, APIUsageRecord)
            assert call_args.model == "anthropic/claude-3.5-sonnet"
            assert call_args.tokens_used == 1500
            assert call_args.cost_usd == 0.045
            assert call_args.request_type == "cv_analysis"
    
    @pytest.mark.asyncio
    async def test_daily_summary_calculation(self, cost_tracker):
        """Test daily cost summary calculation."""
        # Mock the file reading
        mock_records = [
            {
                "model": "anthropic/claude-3.5-sonnet",
                "cost_usd": 0.045,
                "tokens_used": 1500,
                "request_type": "cv_analysis",
                "success": True
            },
            {
                "model": "openai/gpt-4",
                "cost_usd": 0.12,
                "tokens_used": 800,
                "request_type": "job_match",
                "success": True
            }
        ]
        
        with patch('builtins.open'), \
             patch('json.load', return_value=mock_records), \
             patch('pathlib.Path.exists', return_value=True):
            
            summary = await cost_tracker._calculate_daily_costs()
            
            # Assertions
            assert summary["total_cost"] == 0.165  # 0.045 + 0.12
            assert summary["total_requests"] == 2
            assert summary["total_tokens"] == 2300  # 1500 + 800
            assert summary["success_rate"] == 1.0
            assert summary["by_model"]["anthropic/claude-3.5-sonnet"] == 0.045
            assert summary["by_model"]["openai/gpt-4"] == 0.12
    
    @pytest.mark.asyncio
    async def test_cost_alerts(self, cost_tracker):
        """Test cost alert generation."""
        # Set low limits for testing
        cost_tracker.daily_limit = 0.1
        cost_tracker.monthly_limit = 1.0
        
        # Mock high daily costs
        with patch.object(cost_tracker, 'get_daily_summary') as mock_daily, \
             patch.object(cost_tracker, 'get_monthly_summary') as mock_monthly:
            
            mock_daily.return_value = {"total_cost": 0.15}  # Over daily limit
            mock_monthly.return_value = {"total_cost": 0.5}  # Under monthly limit
            
            # Create a mock record
            record = APIUsageRecord(
                model="test_model",
                request_type="test",
                tokens_used=100,
                cost_usd=0.05
            )
            
            alerts = await cost_tracker._check_cost_alerts(record)
            
            # Should generate daily limit alert
            assert len(alerts) > 0
            assert any(alert.alert_type == "daily_limit" for alert in alerts)

class TestCVProcessor:
    """Test cases for CV processing functionality."""
    
    @pytest.fixture
    def cv_processor(self):
        """Create a test CV processor."""
        return CVProcessor()
    
    @pytest.mark.asyncio
    async def test_extract_text_from_txt(self, cv_processor):
        """Test text extraction from TXT file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            test_content = "This is a test CV content.\nExperience: 3 years Python development."
            f.write(test_content)
            f.flush()
            
            result = await cv_processor._extract_from_txt(f.name)
            assert result == test_content
    
    def test_job_status_tracking(self, cv_processor):
        """Test job status tracking."""
        job = ProcessingJob(
            job_id="test_job_123",
            user_email="<EMAIL>",
            file_name="test_cv.pdf",
            file_path="/tmp/test_cv.pdf"
        )
        
        cv_processor.jobs[job.job_id] = job
        
        # Test getting job status
        status = cv_processor.get_job_status(job.job_id)
        assert status is not None
        assert status.job_id == "test_job_123"
        assert status.status == "pending"
        assert status.progress == 0.0
    
    def test_user_jobs_filtering(self, cv_processor):
        """Test filtering jobs by user."""
        # Create test jobs for different users
        job1 = ProcessingJob(
            job_id="job1",
            user_email="<EMAIL>",
            file_name="cv1.pdf",
            file_path="/tmp/cv1.pdf"
        )
        
        job2 = ProcessingJob(
            job_id="job2",
            user_email="<EMAIL>",
            file_name="cv2.pdf",
            file_path="/tmp/cv2.pdf"
        )
        
        job3 = ProcessingJob(
            job_id="job3",
            user_email="<EMAIL>",
            file_name="cv3.pdf",
            file_path="/tmp/cv3.pdf"
        )
        
        cv_processor.jobs.update({
            job1.job_id: job1,
            job2.job_id: job2,
            job3.job_id: job3
        })
        
        # Test filtering
        user1_jobs = cv_processor.get_user_jobs("<EMAIL>")
        assert len(user1_jobs) == 2
        assert all(job.user_email == "<EMAIL>" for job in user1_jobs)
        
        user2_jobs = cv_processor.get_user_jobs("<EMAIL>")
        assert len(user2_jobs) == 1
        assert user2_jobs[0].user_email == "<EMAIL>"

# Integration tests
class TestAIIntegration:
    """Integration tests for AI features."""
    
    @pytest.mark.asyncio
    async def test_full_cv_processing_flow(self):
        """Test the complete CV processing flow."""
        # This would test the full flow from upload to analysis
        # For now, just verify the components can work together
        
        client = OpenRouterClient(api_key="test_key")
        cost_tracker = CostTracker()
        cv_processor = CVProcessor()
        
        # Verify all components are properly initialized
        assert client is not None
        assert cost_tracker is not None
        assert cv_processor is not None
        
        # Test that they can interact (mock the actual API calls)
        with patch.object(client, '_make_request') as mock_request:
            mock_response = Mock()
            mock_response.content = json.dumps({
                "skills": ["Python"],
                "experience_level": "mid",
                "years_experience": 3,
                "job_titles": ["Developer"],
                "industries": ["Tech"],
                "strengths": ["Coding"],
                "improvement_areas": ["Leadership"],
                "recommended_job_types": ["Software Engineer"],
                "confidence_score": 0.8
            })
            mock_request.return_value = mock_response
            
            # This would normally be called by the CV processor
            result = await client.analyze_cv("Test CV content")
            assert isinstance(result, CVAnalysis)

if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
