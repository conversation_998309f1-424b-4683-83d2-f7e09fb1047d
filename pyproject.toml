[project]
name = "job-applying-agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "alembic>=1.16.1",
    "anthropic>=0.54.0",
    "cryptography>=45.0.4",
    "email-validator>=2.2.0",
    "extra-streamlit-components>=0.1.80",
    "httpx>=0.28.1",
    "openai>=1.86.0",
    "pandas>=2.3.0",
    "playwright>=1.52.0",
    "plotly>=6.1.2",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.9.1",
    "pypdf2>=3.0.1",
    "python-docx>=1.1.2",
    "python-dotenv>=1.1.0",
    "sqlalchemy>=2.0.41",
    "streamlit>=1.45.1",
    "streamlit-antd-components>=0.3.2",
    "streamlit-option-menu>=0.4.0",
    "streamlit-pydantic>=0.6.0",
    "streamlit-shadcn-ui>=0.1.18",
]
