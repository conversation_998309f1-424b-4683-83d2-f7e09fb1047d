"""
Enhanced Credentials Setup Component
Beautiful UI for API credential management with quick links and validation.
"""

import streamlit as st
import streamlit_shadcn_ui as ui
from typing import Dict, Any, Optional, List
import requests
from utils.account_manager import AccountManager
from styles.theme import COLORS, TYPOGRAPHY

class EnhancedCredentialManager:
    """Enhanced credential management with better UX."""
    
    def __init__(self, account_manager: AccountManager):
        self.account_manager = account_manager
        
        # Service configurations with quick links
        self.services = {
            'anthropic': {
                'name': 'Anthropic Claude',
                'description': 'Advanced AI for CV optimization and cover letters',
                'icon': '🧠',
                'signup_url': 'https://console.anthropic.com/',
                'docs_url': 'https://docs.anthropic.com/claude/docs/getting-started',
                'api_key_url': 'https://console.anthropic.com/settings/keys',
                'placeholder': 'sk-ant-api03-...',
                'test_model': 'claude-3-haiku-********'
            },
            'openai': {
                'name': 'OpenAI GPT',
                'description': 'Powerful language model for job applications',
                'icon': '🤖',
                'signup_url': 'https://platform.openai.com/signup',
                'docs_url': 'https://platform.openai.com/docs/quickstart',
                'api_key_url': 'https://platform.openai.com/api-keys',
                'placeholder': 'sk-proj-...',
                'test_model': 'gpt-4o-mini'
            },
            'perplexity': {
                'name': 'Perplexity AI',
                'description': 'Real-time web search and research capabilities',
                'icon': '🔍',
                'signup_url': 'https://www.perplexity.ai/',
                'docs_url': 'https://docs.perplexity.ai/',
                'api_key_url': 'https://www.perplexity.ai/settings/api',
                'placeholder': 'pplx-...',
                'test_model': 'llama-3.1-sonar-small-128k-online'
            }
        }
    
    def render_setup_form(self, user_id: str) -> Dict[str, bool]:
        """Render enhanced credential setup form."""
        st.markdown(f"""
        <div style="text-align: center; margin-bottom: 2rem;">
            <h2 style="
                color: {COLORS['text_primary']};
                font-size: {TYPOGRAPHY['font_sizes']['2xl']};
                margin-bottom: 0.5rem;
            ">🔐 API Credentials Setup</h2>
            <p style="
                color: {COLORS['text_secondary']};
                font-size: {TYPOGRAPHY['font_sizes']['base']};
            ">Configure your AI services for enhanced job search capabilities</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Service selection tabs
        service_tabs = st.tabs([f"{config['icon']} {config['name']}" for config in self.services.values()])
        
        setup_status = {}
        
        for i, (service_key, service_config) in enumerate(self.services.items()):
            with service_tabs[i]:
                setup_status[service_key] = self._render_service_setup(user_id, service_key, service_config)
        
        return setup_status
    
    def _render_service_setup(self, user_id: str, service_key: str, config: Dict[str, Any]) -> bool:
        """Render setup form for a specific service."""
        # Service header
        st.markdown(f"""
        <div style="
            background: {COLORS['surface']};
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid {COLORS['border']};
        ">
            <h3 style="
                color: {COLORS['text_primary']};
                margin-bottom: 0.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            ">
                {config['icon']} {config['name']}
            </h3>
            <p style="
                color: {COLORS['text_secondary']};
                margin-bottom: 1rem;
            ">{config['description']}</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Quick links
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown(f"""
            <a href="{config['signup_url']}" target="_blank" style="
                display: inline-block;
                background: {COLORS['accent']};
                color: {COLORS['text_primary']};
                padding: 0.5rem 1rem;
                border-radius: 8px;
                text-decoration: none;
                font-weight: 500;
                text-align: center;
                width: 100%;
                box-sizing: border-box;
            ">
                📝 Sign Up
            </a>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <a href="{config['api_key_url']}" target="_blank" style="
                display: inline-block;
                background: {COLORS['success']};
                color: {COLORS['text_primary']};
                padding: 0.5rem 1rem;
                border-radius: 8px;
                text-decoration: none;
                font-weight: 500;
                text-align: center;
                width: 100%;
                box-sizing: border-box;
            ">
                🔑 Get API Key
            </a>
            """, unsafe_allow_html=True)
        
        with col3:
            st.markdown(f"""
            <a href="{config['docs_url']}" target="_blank" style="
                display: inline-block;
                background: {COLORS['info']};
                color: {COLORS['text_primary']};
                padding: 0.5rem 1rem;
                border-radius: 8px;
                text-decoration: none;
                font-weight: 500;
                text-align: center;
                width: 100%;
                box-sizing: border-box;
            ">
                📚 Documentation
            </a>
            """, unsafe_allow_html=True)
        
        st.markdown("<br>", unsafe_allow_html=True)
        
        # Check if credentials already exist
        existing_creds = self.account_manager.load_credentials(user_id, service_key)
        
        if existing_creds:
            st.success(f"✅ {config['name']} credentials are already configured!")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button(f"Test {config['name']}", key=f"test_{service_key}", use_container_width=True):
                    self._test_credentials(service_key, existing_creds, config)
            
            with col2:
                if st.button(f"Update {config['name']}", key=f"update_{service_key}", use_container_width=True):
                    st.session_state[f"update_{service_key}"] = True
                    st.rerun()
            
            # Show update form if requested
            if st.session_state.get(f"update_{service_key}", False):
                self._render_credential_form(user_id, service_key, config, existing_creds.get('api_key', ''))
            
            return True
        else:
            # Render credential input form
            return self._render_credential_form(user_id, service_key, config)
    
    def _render_credential_form(self, user_id: str, service_key: str, config: Dict[str, Any], 
                               existing_key: str = "") -> bool:
        """Render credential input form."""
        with st.form(f"{service_key}_form"):
            st.subheader(f"Enter {config['name']} API Key")
            
            api_key = st.text_input(
                "API Key",
                value=existing_key,
                type="password",
                placeholder=config['placeholder'],
                help=f"Get your API key from {config['api_key_url']}"
            )
            
            col1, col2 = st.columns(2)
            
            with col1:
                test_button = st.form_submit_button(
                    f"🔍 Test & Save",
                    use_container_width=True,
                    type="primary"
                )
            
            with col2:
                save_button = st.form_submit_button(
                    f"💾 Save Only",
                    use_container_width=True
                )
            
            if test_button and api_key:
                # Test the API key first
                if self._test_api_key(service_key, api_key, config):
                    # Save if test passes
                    if self.account_manager.save_credentials(user_id, service_key, {'api_key': api_key}):
                        st.success(f"✅ {config['name']} credentials saved successfully!")
                        st.session_state[f"update_{service_key}"] = False
                        st.rerun()
                        return True
                else:
                    st.error(f"❌ {config['name']} API key test failed")
            
            elif save_button and api_key:
                # Save without testing
                if self.account_manager.save_credentials(user_id, service_key, {'api_key': api_key}):
                    st.success(f"💾 {config['name']} credentials saved (not tested)")
                    st.session_state[f"update_{service_key}"] = False
                    st.rerun()
                    return True
        
        return False
    
    def _test_api_key(self, service_key: str, api_key: str, config: Dict[str, Any]) -> bool:
        """Test API key validity."""
        try:
            if service_key == 'openai':
                import openai
                client = openai.OpenAI(api_key=api_key)
                response = client.chat.completions.create(
                    model=config['test_model'],
                    messages=[{"role": "user", "content": "Hello"}],
                    max_tokens=5
                )
                return True
                
            elif service_key == 'anthropic':
                import anthropic
                client = anthropic.Anthropic(api_key=api_key)
                response = client.messages.create(
                    model=config['test_model'],
                    max_tokens=5,
                    messages=[{"role": "user", "content": "Hello"}]
                )
                return True
                
            elif service_key == 'perplexity':
                headers = {
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                }
                data = {
                    'model': config['test_model'],
                    'messages': [{'role': 'user', 'content': 'Hello'}],
                    'max_tokens': 5
                }
                response = requests.post(
                    'https://api.perplexity.ai/chat/completions',
                    headers=headers,
                    json=data,
                    timeout=10
                )
                return response.status_code == 200
                
        except Exception as e:
            st.error(f"API test error: {str(e)}")
            return False
        
        return False
    
    def _test_credentials(self, service_key: str, credentials: Dict[str, Any], config: Dict[str, Any]):
        """Test existing credentials."""
        with st.spinner(f"Testing {config['name']} connection..."):
            if self._test_api_key(service_key, credentials['api_key'], config):
                st.success(f"✅ {config['name']} connection successful!")
            else:
                st.error(f"❌ {config['name']} connection failed")
    
    def get_setup_summary(self, user_id: str) -> Dict[str, Any]:
        """Get summary of credential setup status."""
        summary = {
            'total_services': len(self.services),
            'configured_services': 0,
            'services_status': {}
        }
        
        for service_key, config in self.services.items():
            creds = self.account_manager.load_credentials(user_id, service_key)
            is_configured = creds is not None
            
            summary['services_status'][service_key] = {
                'name': config['name'],
                'configured': is_configured,
                'icon': config['icon']
            }
            
            if is_configured:
                summary['configured_services'] += 1
        
        summary['completion_percentage'] = (summary['configured_services'] / summary['total_services']) * 100
        
        return summary
