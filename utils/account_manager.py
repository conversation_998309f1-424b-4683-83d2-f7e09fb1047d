"""
JobFlow Account Management System
Handles user accounts, credentials, caching, and Google Drive sync.
"""

import json
import os
import hashlib
import base64
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import streamlit as st
from pathlib import Path

class AccountManager:
    """Manages user accounts, credentials, and data persistence."""
    
    def __init__(self):
        self.app_dir = Path.home() / ".jobflow"
        self.accounts_file = self.app_dir / "accounts.json"
        self.cache_dir = self.app_dir / "cache"
        self.credentials_dir = self.app_dir / "credentials"
        
        # Create directories if they don't exist
        self.app_dir.mkdir(exist_ok=True)
        self.cache_dir.mkdir(exist_ok=True)
        self.credentials_dir.mkdir(exist_ok=True)
        
        # Initialize encryption
        self.master_key = self._get_or_create_master_key()
        self.cipher = Fernet(self.master_key)
    
    def _get_or_create_master_key(self) -> bytes:
        """Get or create master encryption key."""
        key_file = self.app_dir / "master.key"
        
        if key_file.exists():
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            # Set restrictive permissions
            os.chmod(key_file, 0o600)
            return key
    
    def _derive_key_from_password(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key from password."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def create_account(self, username: str, email: str, password: str) -> Dict[str, Any]:
        """Create a new user account."""
        # Load existing accounts
        accounts = self._load_accounts()
        
        # Check if username or email already exists
        for account in accounts.values():
            if account['username'] == username:
                raise ValueError("Username already exists")
            if account['email'] == email:
                raise ValueError("Email already exists")
        
        # Generate unique user ID
        user_id = hashlib.sha256(f"{username}{email}{datetime.now()}".encode()).hexdigest()[:16]
        
        # Generate salt for password encryption
        salt = os.urandom(16)
        
        # Create account data
        account_data = {
            'user_id': user_id,
            'username': username,
            'email': email,
            'created_at': datetime.now().isoformat(),
            'last_login': None,
            'password_salt': base64.b64encode(salt).decode(),
            'password_hash': hashlib.sha256(password.encode() + salt).hexdigest(),
            'settings': {
                'theme': 'dark',
                'notifications': True,
                'auto_save': True,
                'google_drive_sync': False,
            },
            'credentials': {},
            'progress': {
                'onboarding_completed': False,
                'credentials_setup': False,
                'first_job_search': False,
            }
        }
        
        # Save account
        accounts[user_id] = account_data
        self._save_accounts(accounts)
        
        # Create user-specific directories
        user_dir = self.cache_dir / user_id
        user_dir.mkdir(exist_ok=True)
        (user_dir / "jobs").mkdir(exist_ok=True)
        (user_dir / "searches").mkdir(exist_ok=True)
        (user_dir / "exports").mkdir(exist_ok=True)
        
        return account_data
    
    def authenticate(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user and return account data."""
        accounts = self._load_accounts()
        
        for account in accounts.values():
            if account['username'] == username:
                # Verify password
                salt = base64.b64decode(account['password_salt'])
                password_hash = hashlib.sha256(password.encode() + salt).hexdigest()
                
                if password_hash == account['password_hash']:
                    # Update last login
                    account['last_login'] = datetime.now().isoformat()
                    accounts[account['user_id']] = account
                    self._save_accounts(accounts)
                    return account
        
        return None
    
    def get_account(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get account data by user ID."""
        accounts = self._load_accounts()
        return accounts.get(user_id)
    
    def update_account(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """Update account data."""
        accounts = self._load_accounts()
        
        if user_id in accounts:
            accounts[user_id].update(updates)
            self._save_accounts(accounts)
            return True
        
        return False
    
    def save_credentials(self, user_id: str, service: str, credentials: Dict[str, Any]) -> bool:
        """Save encrypted credentials for a service."""
        try:
            # Encrypt credentials
            credentials_json = json.dumps(credentials)
            encrypted_data = self.cipher.encrypt(credentials_json.encode())
            
            # Save to file
            cred_file = self.credentials_dir / f"{user_id}_{service}.cred"
            with open(cred_file, 'wb') as f:
                f.write(encrypted_data)
            
            # Set restrictive permissions
            os.chmod(cred_file, 0o600)
            
            # Update account record
            accounts = self._load_accounts()
            if user_id in accounts:
                if 'credentials' not in accounts[user_id]:
                    accounts[user_id]['credentials'] = {}
                accounts[user_id]['credentials'][service] = {
                    'saved_at': datetime.now().isoformat(),
                    'encrypted': True
                }
                self._save_accounts(accounts)
            
            return True
        except Exception as e:
            st.error(f"Failed to save credentials: {str(e)}")
            return False
    
    def load_credentials(self, user_id: str, service: str) -> Optional[Dict[str, Any]]:
        """Load and decrypt credentials for a service."""
        try:
            cred_file = self.credentials_dir / f"{user_id}_{service}.cred"
            
            if not cred_file.exists():
                return None
            
            # Read and decrypt
            with open(cred_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher.decrypt(encrypted_data)
            credentials = json.loads(decrypted_data.decode())
            
            return credentials
        except Exception as e:
            st.error(f"Failed to load credentials: {str(e)}")
            return None
    
    def _load_accounts(self) -> Dict[str, Any]:
        """Load accounts from file."""
        if self.accounts_file.exists():
            try:
                with open(self.accounts_file, 'r') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}
    
    def _save_accounts(self, accounts: Dict[str, Any]) -> None:
        """Save accounts to file."""
        with open(self.accounts_file, 'w') as f:
            json.dump(accounts, f, indent=2)
        # Set restrictive permissions
        os.chmod(self.accounts_file, 0o600)
    
    def cache_job_data(self, user_id: str, job_data: Dict[str, Any]) -> bool:
        """Cache job search data."""
        try:
            cache_file = self.cache_dir / user_id / "jobs" / f"job_{job_data.get('id', 'unknown')}.json"
            with open(cache_file, 'w') as f:
                json.dump({
                    'data': job_data,
                    'cached_at': datetime.now().isoformat(),
                    'expires_at': (datetime.now() + timedelta(days=7)).isoformat()
                }, f, indent=2)
            return True
        except Exception as e:
            st.error(f"Failed to cache job data: {str(e)}")
            return False
    
    def get_cached_jobs(self, user_id: str) -> List[Dict[str, Any]]:
        """Get cached job data."""
        jobs = []
        jobs_dir = self.cache_dir / user_id / "jobs"
        
        if not jobs_dir.exists():
            return jobs
        
        for job_file in jobs_dir.glob("job_*.json"):
            try:
                with open(job_file, 'r') as f:
                    cached_data = json.load(f)
                
                # Check if cache is still valid
                expires_at = datetime.fromisoformat(cached_data['expires_at'])
                if datetime.now() < expires_at:
                    jobs.append(cached_data['data'])
                else:
                    # Remove expired cache
                    job_file.unlink()
            except Exception:
                continue
        
        return jobs
    
    def export_user_data(self, user_id: str, include_credentials: bool = False) -> Dict[str, Any]:
        """Export user data for backup or transfer."""
        account = self.get_account(user_id)
        if not account:
            return {}
        
        export_data = {
            'account': account.copy(),
            'jobs': self.get_cached_jobs(user_id),
            'exported_at': datetime.now().isoformat(),
            'version': '1.0'
        }
        
        # Remove sensitive data if not including credentials
        if not include_credentials:
            export_data['account'].pop('password_hash', None)
            export_data['account'].pop('password_salt', None)
            export_data['account'].pop('credentials', None)
        
        return export_data
