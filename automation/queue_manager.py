"""
Automated Queue Manager for Job Applications
Handles background job application processing with state management and cost tracking.
"""

import asyncio
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from pydantic import BaseModel, Field
import json
from enum import Enum

from models import JobPortalName, ApplicationStatus
from database.manager import db_manager
from automation.api_client import api_manager
from ai.openrouter_client import openrouter_client
from ai.cost_tracker import CostTracker
from utils.logger import get_logger

logger = get_logger(__name__)

class QueueJobType(str, Enum):
    """Types of queue jobs."""
    JOB_SEARCH = "job_search"
    JOB_APPLICATION = "job_application"
    CV_OPTIMIZATION = "cv_optimization"
    COVER_LETTER_GENERATION = "cover_letter_generation"

class QueueJobStatus(str, Enum):
    """Status of queue jobs."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class QueueJob(BaseModel):
    """Model for queue jobs."""
    job_id: str
    job_type: QueueJobType
    user_email: str
    status: QueueJobStatus = Field(default=QueueJobStatus.PENDING)
    priority: int = Field(default=5, ge=1, le=10)  # 1 = highest, 10 = lowest
    
    # Job parameters
    parameters: Dict[str, Any] = Field(default_factory=dict)
    
    # Progress tracking
    progress: float = Field(default=0.0, ge=0.0, le=1.0)
    current_step: str = ""
    total_steps: int = 1
    
    # Timing
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_duration: Optional[int] = None  # seconds
    
    # Results and errors
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    
    # Cost tracking
    estimated_cost: Optional[float] = None
    actual_cost: Optional[float] = None
    
    # Dependencies
    depends_on: List[str] = Field(default_factory=list)  # Job IDs this job depends on
    
    # Auto-run settings
    auto_retry: bool = False
    max_retries: int = 3
    retry_count: int = 0

class AutoRunConfig(BaseModel):
    """Configuration for auto-run mode."""
    enabled: bool = False
    user_email: str
    
    # Search parameters
    keywords: List[str]
    locations: List[str]
    portals: List[JobPortalName]
    
    # Application limits
    daily_application_limit: int = 10
    applications_per_portal: int = 5
    
    # Timing
    run_schedule: str = "daily"  # daily, weekly, manual
    run_time: str = "09:00"  # HH:MM format
    
    # AI settings
    use_ai_optimization: bool = True
    ai_cost_limit_daily: float = 5.0
    
    # Filters
    min_match_score: float = 0.7
    exclude_companies: List[str] = Field(default_factory=list)
    required_keywords: List[str] = Field(default_factory=list)

class QueueManager:
    """Manages the job application queue with auto-run capabilities."""
    
    def __init__(self):
        self.jobs: Dict[str, QueueJob] = {}
        self.queue = asyncio.Queue()
        self.is_running = False
        self.worker_count = 2  # Number of concurrent workers
        self.workers: List[asyncio.Task] = []
        
        self.data_dir = Path("data/queue")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.cost_tracker = CostTracker()
        
        # Auto-run configurations
        self.auto_run_configs: Dict[str, AutoRunConfig] = {}
        
        # Load existing jobs and configs
        self._load_persistent_data()
        
        # Start queue processing
        self._start_queue_processing()
    
    def _start_queue_processing(self):
        """Start the background queue processing."""
        if not self.is_running:
            self.is_running = True
            thread = threading.Thread(target=self._run_queue_processor, daemon=True)
            thread.start()
    
    def _run_queue_processor(self):
        """Run the queue processor in a separate thread."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Start worker tasks
        for i in range(self.worker_count):
            worker = loop.create_task(self._queue_worker(f"worker-{i}"))
            self.workers.append(worker)
        
        # Start auto-run scheduler
        scheduler = loop.create_task(self._auto_run_scheduler())
        
        try:
            loop.run_until_complete(asyncio.gather(*self.workers, scheduler))
        except Exception as e:
            logger.error(f"Queue processor error: {str(e)}")
        finally:
            loop.close()
    
    async def _queue_worker(self, worker_name: str):
        """Worker that processes jobs from the queue."""
        logger.info(f"Queue worker {worker_name} started")
        
        while self.is_running:
            try:
                # Get next job from queue
                job_id = await asyncio.wait_for(self.queue.get(), timeout=1.0)
                
                if job_id in self.jobs:
                    job = self.jobs[job_id]
                    logger.info(f"Worker {worker_name} processing job {job_id}")
                    
                    await self._process_job(job)
                    
                    # Mark queue task as done
                    self.queue.task_done()
                
            except asyncio.TimeoutError:
                # No jobs in queue, continue waiting
                continue
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {str(e)}")
    
    async def _process_job(self, job: QueueJob):
        """Process a single job."""
        try:
            # Update job status
            job.status = QueueJobStatus.RUNNING
            job.started_at = datetime.now()
            await self._save_job(job)
            
            # Process based on job type
            if job.job_type == QueueJobType.JOB_SEARCH:
                await self._process_job_search(job)
            elif job.job_type == QueueJobType.JOB_APPLICATION:
                await self._process_job_application(job)
            elif job.job_type == QueueJobType.CV_OPTIMIZATION:
                await self._process_cv_optimization(job)
            elif job.job_type == QueueJobType.COVER_LETTER_GENERATION:
                await self._process_cover_letter_generation(job)
            else:
                raise ValueError(f"Unknown job type: {job.job_type}")
            
            # Mark as completed
            job.status = QueueJobStatus.COMPLETED
            job.completed_at = datetime.now()
            job.progress = 1.0
            
            logger.info(f"Job {job.job_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Error processing job {job.job_id}: {str(e)}")
            job.status = QueueJobStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.now()
            
            # Retry if configured
            if job.auto_retry and job.retry_count < job.max_retries:
                job.retry_count += 1
                job.status = QueueJobStatus.PENDING
                job.started_at = None
                job.error_message = None
                await self.queue.put(job.job_id)
                logger.info(f"Job {job.job_id} queued for retry ({job.retry_count}/{job.max_retries})")
        
        finally:
            await self._save_job(job)
    
    async def _process_job_search(self, job: QueueJob):
        """Process a job search job."""
        params = job.parameters
        
        job.current_step = "Searching job portals"
        job.progress = 0.2
        await self._save_job(job)
        
        # Search jobs
        results = await api_manager.search_all_portals(
            keywords=params.get('keywords', []),
            location=params.get('location', ''),
            enabled_portals=params.get('portals', [])
        )
        
        job.current_step = "Processing results"
        job.progress = 0.8
        await self._save_job(job)
        
        # Store results
        job.result = {
            'total_jobs': sum(len(jobs) for jobs in results.values()),
            'results_by_portal': {portal.value: len(jobs) for portal, jobs in results.items()},
            'jobs': results
        }
        
        job.progress = 1.0
    
    async def _process_job_application(self, job: QueueJob):
        """Process a job application job."""
        params = job.parameters
        
        job.current_step = "Preparing application"
        job.progress = 0.1
        await self._save_job(job)
        
        # Get job details
        job_data = params.get('job_data', {})
        
        # Generate cover letter if AI is enabled
        if params.get('use_ai', False):
            job.current_step = "Generating cover letter"
            job.progress = 0.3
            await self._save_job(job)
            
            # This would use the AI to generate a cover letter
            # For now, use a placeholder
            cover_letter = "AI-generated cover letter placeholder"
        else:
            cover_letter = params.get('cover_letter_template', '')
        
        job.current_step = "Submitting application"
        job.progress = 0.7
        await self._save_job(job)
        
        # Submit application (placeholder - would use browser automation)
        # For now, just save to database
        application_data = {
            'user_email': job.user_email,
            'job_data': job_data,
            'cover_letter': cover_letter,
            'status': ApplicationStatus.APPLIED,
            'submission_timestamp': datetime.now()
        }
        
        # Save to database
        # db_manager.save_job_application(application_data)
        
        job.result = {
            'application_submitted': True,
            'application_id': f"app_{job.job_id}",
            'cover_letter_length': len(cover_letter)
        }
        
        job.progress = 1.0
    
    async def _process_cv_optimization(self, job: QueueJob):
        """Process a CV optimization job."""
        params = job.parameters
        
        job.current_step = "Analyzing job requirements"
        job.progress = 0.2
        await self._save_job(job)
        
        # Use AI to optimize CV
        cv_text = params.get('cv_text', '')
        job_description = params.get('job_description', '')
        
        if not cv_text or not job_description:
            raise ValueError("CV text and job description required for optimization")
        
        job.current_step = "Optimizing CV with AI"
        job.progress = 0.6
        await self._save_job(job)
        
        # This would use the AI client to optimize the CV
        # For now, return a placeholder
        optimized_cv = f"Optimized CV for job: {job_description[:100]}..."
        
        job.result = {
            'optimized_cv': optimized_cv,
            'original_length': len(cv_text),
            'optimized_length': len(optimized_cv)
        }
        
        job.progress = 1.0
    
    async def _process_cover_letter_generation(self, job: QueueJob):
        """Process a cover letter generation job."""
        params = job.parameters
        
        job.current_step = "Analyzing job posting"
        job.progress = 0.3
        await self._save_job(job)
        
        # Generate cover letter using AI
        job_description = params.get('job_description', '')
        company_name = params.get('company_name', '')
        position_title = params.get('position_title', '')
        
        job.current_step = "Generating personalized cover letter"
        job.progress = 0.7
        await self._save_job(job)
        
        # This would use the AI client to generate cover letter
        cover_letter = f"Dear {company_name} team,\n\nI am writing to express my interest in the {position_title} position..."
        
        job.result = {
            'cover_letter': cover_letter,
            'word_count': len(cover_letter.split()),
            'company_name': company_name,
            'position_title': position_title
        }
        
        job.progress = 1.0
    
    async def _auto_run_scheduler(self):
        """Scheduler for auto-run jobs."""
        while self.is_running:
            try:
                # Check each auto-run config
                for user_email, config in self.auto_run_configs.items():
                    if config.enabled:
                        await self._check_auto_run_schedule(config)
                
                # Sleep for 1 minute before next check
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"Auto-run scheduler error: {str(e)}")
                await asyncio.sleep(60)
    
    async def _check_auto_run_schedule(self, config: AutoRunConfig):
        """Check if auto-run should be triggered for a config."""
        now = datetime.now()
        
        # Check if it's time to run based on schedule
        if config.run_schedule == "daily":
            # Check if we should run today
            run_time = datetime.strptime(config.run_time, "%H:%M").time()
            scheduled_time = datetime.combine(now.date(), run_time)
            
            # Run if it's past the scheduled time and we haven't run today
            if now >= scheduled_time:
                last_run = await self._get_last_auto_run(config.user_email)
                if not last_run or last_run.date() < now.date():
                    await self._trigger_auto_run(config)
    
    async def _trigger_auto_run(self, config: AutoRunConfig):
        """Trigger an auto-run sequence."""
        logger.info(f"Triggering auto-run for user: {config.user_email}")
        
        # Check daily cost limit
        daily_costs = await self.cost_tracker.get_daily_summary()
        if daily_costs.get('total_cost', 0) >= config.ai_cost_limit_daily:
            logger.warning(f"Auto-run skipped for {config.user_email}: daily cost limit reached")
            return
        
        # Create job search job
        search_job = await self.add_job(
            job_type=QueueJobType.JOB_SEARCH,
            user_email=config.user_email,
            parameters={
                'keywords': config.keywords,
                'location': config.locations[0] if config.locations else '',
                'portals': config.portals
            },
            priority=1  # High priority for auto-run
        )
        
        # Save auto-run timestamp
        await self._save_auto_run_timestamp(config.user_email)
    
    async def add_job(self, job_type: QueueJobType, user_email: str, 
                     parameters: Dict[str, Any], priority: int = 5,
                     auto_retry: bool = False, depends_on: List[str] = None) -> str:
        """Add a new job to the queue."""
        
        job_id = f"{job_type.value}_{user_email}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        job = QueueJob(
            job_id=job_id,
            job_type=job_type,
            user_email=user_email,
            parameters=parameters,
            priority=priority,
            auto_retry=auto_retry,
            depends_on=depends_on or []
        )
        
        # Estimate cost and duration
        job.estimated_cost = self._estimate_job_cost(job)
        job.estimated_duration = self._estimate_job_duration(job)
        
        self.jobs[job_id] = job
        await self._save_job(job)
        
        # Add to queue if no dependencies or dependencies are met
        if not job.depends_on or await self._dependencies_met(job):
            await self.queue.put(job_id)
        
        logger.info(f"Job {job_id} added to queue")
        return job_id
    
    def _estimate_job_cost(self, job: QueueJob) -> float:
        """Estimate the cost of a job."""
        cost_estimates = {
            QueueJobType.JOB_SEARCH: 0.0,  # No AI cost
            QueueJobType.JOB_APPLICATION: 0.05,  # Small AI cost for cover letter
            QueueJobType.CV_OPTIMIZATION: 0.10,  # Medium AI cost
            QueueJobType.COVER_LETTER_GENERATION: 0.03  # Small AI cost
        }
        return cost_estimates.get(job.job_type, 0.0)
    
    def _estimate_job_duration(self, job: QueueJob) -> int:
        """Estimate the duration of a job in seconds."""
        duration_estimates = {
            QueueJobType.JOB_SEARCH: 30,
            QueueJobType.JOB_APPLICATION: 60,
            QueueJobType.CV_OPTIMIZATION: 45,
            QueueJobType.COVER_LETTER_GENERATION: 30
        }
        return duration_estimates.get(job.job_type, 30)
    
    async def _dependencies_met(self, job: QueueJob) -> bool:
        """Check if job dependencies are met."""
        for dep_job_id in job.depends_on:
            if dep_job_id in self.jobs:
                dep_job = self.jobs[dep_job_id]
                if dep_job.status != QueueJobStatus.COMPLETED:
                    return False
            else:
                return False  # Dependency job not found
        return True
    
    async def _save_job(self, job: QueueJob):
        """Save job state to disk."""
        try:
            job_file = self.data_dir / f"{job.job_id}.json"
            with open(job_file, 'w') as f:
                json.dump(job.model_dump(mode='json'), f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving job {job.job_id}: {str(e)}")
    
    def _load_persistent_data(self):
        """Load jobs and configs from disk."""
        try:
            # Load jobs
            for job_file in self.data_dir.glob("*.json"):
                if job_file.stem.startswith(("job_search_", "job_application_", "cv_optimization_", "cover_letter_")):
                    try:
                        with open(job_file, 'r') as f:
                            job_data = json.load(f)
                        job = QueueJob(**job_data)
                        self.jobs[job.job_id] = job
                        
                        # Re-queue pending jobs
                        if job.status == QueueJobStatus.PENDING:
                            asyncio.create_task(self.queue.put(job.job_id))
                            
                    except Exception as e:
                        logger.error(f"Error loading job from {job_file}: {str(e)}")
            
            # Load auto-run configs
            config_file = self.data_dir / "auto_run_configs.json"
            if config_file.exists():
                try:
                    with open(config_file, 'r') as f:
                        configs_data = json.load(f)
                    
                    for user_email, config_data in configs_data.items():
                        self.auto_run_configs[user_email] = AutoRunConfig(**config_data)
                        
                except Exception as e:
                    logger.error(f"Error loading auto-run configs: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error loading persistent data: {str(e)}")
    
    async def _get_last_auto_run(self, user_email: str) -> Optional[datetime]:
        """Get the last auto-run timestamp for a user."""
        timestamp_file = self.data_dir / f"auto_run_{user_email}.txt"
        if timestamp_file.exists():
            try:
                with open(timestamp_file, 'r') as f:
                    timestamp_str = f.read().strip()
                return datetime.fromisoformat(timestamp_str)
            except Exception as e:
                logger.error(f"Error reading auto-run timestamp: {str(e)}")
        return None
    
    async def _save_auto_run_timestamp(self, user_email: str):
        """Save the auto-run timestamp for a user."""
        timestamp_file = self.data_dir / f"auto_run_{user_email}.txt"
        try:
            with open(timestamp_file, 'w') as f:
                f.write(datetime.now().isoformat())
        except Exception as e:
            logger.error(f"Error saving auto-run timestamp: {str(e)}")
    
    def get_job_status(self, job_id: str) -> Optional[QueueJob]:
        """Get the status of a job."""
        return self.jobs.get(job_id)
    
    def get_user_jobs(self, user_email: str, limit: int = 50) -> List[QueueJob]:
        """Get jobs for a specific user."""
        user_jobs = [job for job in self.jobs.values() if job.user_email == user_email]
        return sorted(user_jobs, key=lambda x: x.created_at, reverse=True)[:limit]
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        total_jobs = len(self.jobs)
        pending_jobs = len([j for j in self.jobs.values() if j.status == QueueJobStatus.PENDING])
        running_jobs = len([j for j in self.jobs.values() if j.status == QueueJobStatus.RUNNING])
        completed_jobs = len([j for j in self.jobs.values() if j.status == QueueJobStatus.COMPLETED])
        failed_jobs = len([j for j in self.jobs.values() if j.status == QueueJobStatus.FAILED])
        
        return {
            'total_jobs': total_jobs,
            'pending_jobs': pending_jobs,
            'running_jobs': running_jobs,
            'completed_jobs': completed_jobs,
            'failed_jobs': failed_jobs,
            'queue_size': self.queue.qsize(),
            'workers_active': len(self.workers)
        }

# Global queue manager instance
queue_manager = QueueManager()
